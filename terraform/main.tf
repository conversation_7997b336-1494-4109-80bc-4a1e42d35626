# Terraform configuration for ivent-api AWS infrastructure
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  # Backend configuration for state management
  backend "s3" {
    # This will be configured per environment
    # bucket = "ivent-terraform-state-${var.environment}-${var.aws_region}"
    # key    = "terraform.tfstate"
    # region = var.aws_region
  }
}

# Configure the AWS Provider
provider "aws" {
  region = var.aws_region

  default_tags {
    tags = {
      Project     = var.project_name
      Environment = var.environment
      ManagedBy   = "terraform"
    }
  }
}

# Data sources
data "aws_availability_zones" "available" {
  state = "available"
}

data "aws_caller_identity" "current" {}

# Local values for common configurations
locals {
  name_prefix = "${var.project_name}-${var.environment}"

  common_tags = {
    Project     = var.project_name
    Environment = var.environment
    ManagedBy   = "terraform"
  }

  # Database configuration
  db_name     = replace(local.name_prefix, "-", "_")
  db_username = "ivent_admin"

  # Availability zones (use first 2)
  azs = slice(data.aws_availability_zones.available.names, 0, 2)
}

# VPC Module
module "vpc" {
  source = "./modules/vpc"

  name_prefix = local.name_prefix
  cidr_block  = var.vpc_cidr
  azs         = local.azs

  tags = local.common_tags
}

# Security Groups Module
module "security_groups" {
  source = "./modules/security-groups"

  name_prefix = local.name_prefix
  vpc_id      = module.vpc.vpc_id

  tags = local.common_tags
}

# RDS Module
module "rds" {
  source = "./modules/rds"

  name_prefix          = local.name_prefix
  db_name              = local.db_name
  db_username          = local.db_username
  db_password          = var.db_password
  vpc_id               = module.vpc.vpc_id
  private_subnet_ids   = module.vpc.private_subnet_ids
  db_security_group_id = module.security_groups.db_security_group_id

  instance_class    = var.db_instance_class
  allocated_storage = var.db_allocated_storage
  backup_retention  = var.db_backup_retention
  multi_az          = var.environment == "prod" ? true : false

  tags = local.common_tags
}

# S3 Module
module "s3" {
  source = "./modules/s3"

  name_prefix = local.name_prefix

  tags = local.common_tags
}

# ECS Module
module "ecs" {
  source = "./modules/ecs"

  name_prefix           = local.name_prefix
  environment           = var.environment
  vpc_id                = module.vpc.vpc_id
  private_subnet_ids    = module.vpc.private_subnet_ids
  public_subnet_ids     = module.vpc.public_subnet_ids
  app_security_group_id = module.security_groups.app_security_group_id
  alb_security_group_id = module.security_groups.alb_security_group_id

  # Application configuration
  app_image  = var.app_image
  app_port   = var.app_port
  app_cpu    = var.app_cpu
  app_memory = var.app_memory
  app_count  = var.app_count

  # Environment variables
  environment_variables = {
    NODE_ENV           = var.environment == "prod" ? "production" : "development"
    ENVIRONMENT        = var.environment
    SERVER_HOST        = "0.0.0.0"
    SERVER_TCP_PORT    = tostring(var.app_port)
    POSTGRES_HOST      = module.rds.db_endpoint
    POSTGRES_PORT      = "5432"
    POSTGRES_DB        = local.db_name
    POSTGRES_USER      = local.db_username
    AWS_REGION         = var.aws_region
    AWS_S3_BUCKET_NAME = module.s3.media_bucket_name
    USE_SWAGGER        = var.environment != "prod" ? "true" : "false"
  }

  # Secrets (will be stored in AWS Systems Manager)
  secrets = {
    POSTGRES_PASSWORD   = aws_ssm_parameter.db_password.arn
    JWT_SECRET          = aws_ssm_parameter.jwt_secret.arn
    MAPBOX_ACCESS_TOKEN = aws_ssm_parameter.mapbox_access_token.arn
  }

  tags = local.common_tags
}

# Monitoring Module
module "monitoring" {
  source = "./modules/monitoring"

  name_prefix              = local.name_prefix
  aws_region               = var.aws_region
  ecs_cluster_name         = module.ecs.cluster_name
  ecs_service_name         = module.ecs.service_name
  load_balancer_arn_suffix = module.ecs.load_balancer_arn_suffix
  db_instance_id           = module.rds.db_instance_id
  alert_email              = var.alert_email

  tags = local.common_tags
}

# Systems Manager Parameters for secrets
resource "aws_ssm_parameter" "db_password" {
  name      = "/ivent/${var.environment}/db/password"
  type      = "SecureString"
  value     = var.db_password
  overwrite = true

  tags = local.common_tags
}

resource "aws_ssm_parameter" "jwt_secret" {
  name      = "/ivent/${var.environment}/jwt/secret"
  type      = "SecureString"
  value     = var.jwt_secret
  overwrite = true

  tags = local.common_tags
}

resource "aws_ssm_parameter" "mapbox_access_token" {
  name      = "/ivent/${var.environment}/mapbox/access-token"
  type      = "SecureString"
  value     = var.mapbox_access_token
  overwrite = true

  tags = local.common_tags
}
