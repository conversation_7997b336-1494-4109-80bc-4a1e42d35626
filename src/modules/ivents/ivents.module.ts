import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Ivent, Location } from 'src/entities';
import { IventsController } from './ivents.controller';
import { IventsService } from './ivents.service';

@Module({
  imports: [TypeOrmModule.forFeature([Ivent, Location])],
  providers: [IventsService],
  controllers: [IventsController],
  exports: [],
})
export class IventsModule {}
