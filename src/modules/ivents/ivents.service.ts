import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Ivent, IventCollab, IventDate, IventTag, IventUniversity, Location, MemoryFolder, VibeFolder } from 'src/entities';
import { AccountTypeEnum, IventCollabStatusEnum, IventCreatorTypeEnum, IventPrivacyEnum, IventViewTypeEnum } from 'src/enums';
import { EmptyReturn } from 'src/models/empty-return';
import { hydrateComputedList } from 'src/utils/hydrate-computed-list';
import { insertQueryBuilder } from 'src/utils/insert-query-builder';
import { stringToBooleanTransformer, stringToNumberTransformer } from 'src/utils/value-transformers';
import { DataSource, Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import {
  CreateIventParams,
  DeleteIventByIventIdParams,
  FavoriteIventByIventIdParams,
  GetBannerByIventIdParams,
  GetIventPageByIventIdParams,
  GetLatestIventsParams,
  GetSuggestedImagesParams,
  GetUpcomingIventParams,
  UnfavoriteIventByIventIdParams,
  UpdateDateByIventIdParams,
  UpdateDetailsByIventIdParams,
  UpdateLocationByIventIdParams,
} from './models/ivents.params';
import { CreateIventReturn, GetBannerByIventIdReturn, GetIventPageByIventIdReturn, GetLatestIventsReturn, GetSuggestedImagesReturn, GetUpcomingIventReturn } from './models/ivents.returns';

@Injectable()
export class IventsService {
  constructor(
    private dataSource: DataSource,
    @InjectRepository(Ivent)
    private iventRepository: Repository<Ivent>,
    @InjectRepository(Location)
    private locationRepository: Repository<Location>,
  ) {}

  async createIvent(createIventParams: CreateIventParams): Promise<CreateIventReturn> {
    const {
      sessionId,

      creatorType,
      iventName,
      thumbnailUrl,
      thumbnailBuffer,
      dates,
      mapboxId,
      latitude,
      longitude,
      description,
      categoryTagId,
      tagIds,
      privacy,
      allowedUniversityCodes,
      collabs,
      googleFormsUrl,
      callNumber,
      instagramUsername,
      websiteUrl,
      whatsappUrl,
      whatsappNumber,
      isWhatsappUrlPrivate,
    } = createIventParams;

    let mediaUrl;
    if (thumbnailBuffer) {
      const thumbnailId = uuidv4();
    } else if (thumbnailUrl) {
      mediaUrl = thumbnailUrl;
    }

    const newIvent = new Ivent();
    newIvent.creator_user_id = sessionId;
    newIvent.creator_type = IventCreatorTypeEnum.USER;
    newIvent.ivent_name = iventName;
    newIvent.thumbnail_url = mediaUrl;
    newIvent.description = description || null;
    newIvent.category_tag_id = categoryTagId;
    newIvent.privacy = privacy;
    newIvent.google_forms_url = googleFormsUrl || null;
    newIvent.call_number = callNumber || null;
    newIvent.website_url = websiteUrl || null;
    newIvent.instagram_username = instagramUsername || null;
    newIvent.whatsapp_url = whatsappUrl || null;
    newIvent.whatsapp_number = whatsappNumber || null;
    newIvent.is_whatsapp_url_private = isWhatsappUrlPrivate || false;

    const location = await this.locationRepository.findOneBy({ mapbox_id: mapboxId });
    if (!location) {
      newIvent.location = new Location();
      newIvent.location.location_name = iventName;
      newIvent.location.open_address = iventName;
      newIvent.location.mapbox_id = mapboxId;
      newIvent.location.geom = {
        type: 'Point',
        coordinates: [latitude, longitude],
      };
    } else {
      newIvent.location = location!;
    }

    newIvent.vibe_folder = new VibeFolder();
    newIvent.memory_folder = new MemoryFolder();

    newIvent.tags = tagIds.map((val) => {
      const newIventTag = new IventTag();
      newIventTag.hobby_id = val;
      return newIventTag;
    });

    newIvent.dates = dates.map((val) => {
      const newIventDate = new IventDate();
      newIventDate.ivent_date = new Date(val);
      return newIventDate;
    });

    if (privacy === IventPrivacyEnum.SELECTED_EDU)
      newIvent.universities = allowedUniversityCodes.map((val) => {
        const newIventUniversity = new IventUniversity();
        newIventUniversity.university_code = val;
        return newIventUniversity;
      });

    newIvent.collabs = [...collabs, { id: sessionId, type: AccountTypeEnum.USER }].map((val) => {
      const newIventCollab = new IventCollab();
      newIventCollab.collab_user_id = val.type === AccountTypeEnum.USER ? val.id : null;
      newIventCollab.collab_page_id = val.type === AccountTypeEnum.PAGE ? val.id : null;
      newIventCollab.collab_type = val.type;
      newIventCollab.inviter_id = sessionId;
      newIventCollab.status = val.id === sessionId ? IventCollabStatusEnum.ADMIN : IventCollabStatusEnum.PENDING;
      return newIventCollab;
    });

    const insertedIvent = await this.iventRepository.save(newIvent);

    return {
      iventId: insertedIvent.id,
    };
  }

  async deleteIventByIventId(deleteIventByIventIdParams: DeleteIventByIventIdParams): Promise<EmptyReturn> {
    const { sessionId, iventId } = deleteIventByIventIdParams;

    // Check if ivent exists and user has permission to delete it
    const ivent = await this.iventRepository.findOne({
      where: { id: iventId },
      relations: ['creator_user', 'creator_page'],
    });

    if (!ivent) {
      throw new HttpException('Ivent not found.', HttpStatus.NOT_FOUND);
    }

    // Check if user is the creator (only creators can delete ivents)
    const isCreator = ivent.creator_user_id === sessionId || ivent.creator_page_id === sessionId;
    if (!isCreator) {
      throw new HttpException('You do not have permission to delete this ivent.', HttpStatus.FORBIDDEN);
    }

    // Delete the ivent (cascade will handle related entities)
    await this.iventRepository.remove(ivent);

    return {};
  }

  async getIventPageByIventId(getIventPageByIventIdParams: GetIventPageByIventIdParams): Promise<GetIventPageByIventIdReturn> {
    const { sessionId, iventId } = getIventPageByIventIdParams;

    const { entities, raw } = await this.dataSource
      .getRepository(Ivent)
      .createQueryBuilder('i')
      .leftJoinAndSelect('i.location', 'l')
      .leftJoinAndSelect('i.creator_page', 'p')
      .leftJoinAndSelect('i.creator_user', 'u')
      .leftJoinAndSelect('i.creator_distributor', 'dis')
      .leftJoinAndSelect('i.category_tag', 'h')
      .leftJoinAndSelect('i.ivent_dates_aggregated', 'd')
      .leftJoinAndSelect('i.ivent_tags_aggregated', 'ita')
      .leftJoinAndSelect('i.active_session_ivents', 'asi', 'asi.account_id = :sessionId', { sessionId })
      .leftJoinAndSelect('i.collab_summary_of_ivent', 'csof', 'csof.account_id = :sessionId', { sessionId })
      .leftJoinAndSelect('i.member_summary_of_session_squad', 'msoss', 'msoss.user_id = :sessionId', { sessionId })
      .leftJoinAndSelect('i.participant_summary_of_ivent', 'psoi')
      .leftJoinAndSelect('i.session_friend_summary_of_ivent', 'sfsoi', 'sfsoi.user_id = :sessionId', { sessionId })
      .leftJoin(
        (qb) => qb.select('uf.favorited_ivent_id', 'ivent_id').addSelect('COUNT(uf.user_id)', 'favorite_count').from('user_favorites', 'uf').groupBy('uf.favorited_ivent_id'),
        'fcoi',
        'fcoi.ivent_id = i.id',
      )
      .leftJoin('i.favorited_by', 'ufa', 'ufa.user_id = :sessionId', { sessionId })
      .addSelect('fcoi.favorite_count', 'favorite_count')
      .addSelect('CASE WHEN ufa.user_id IS NOT NULL THEN TRUE ELSE FALSE END', 'is_favorited')
      .where('i.id = :iventId', { iventId })
      .getRawAndEntities();

    const ivent = hydrateComputedList(entities, raw, [
      { field: 'favorite_count', transformer: stringToNumberTransformer },
      { field: 'is_favorited', transformer: stringToBooleanTransformer },
    ])[0]!;

    let collabCount = ivent.collab_count;
    let collabNames = ivent.collab_names;
    if (collabNames.includes(ivent.creator_name!)) {
      collabCount -= 1;
      collabNames = collabNames.filter((val) => val !== ivent.creator_name!);
    } else {
      collabNames.pop();
    }

    let isFavorited;
    let favoriteCount;
    let memberFirstnames;
    switch (ivent.view_type) {
      case IventViewTypeEnum.CREATED:
        isFavorited = ivent.is_favorited!;
        favoriteCount = ivent.favorite_count!;
        memberFirstnames = null;
        break;
      case IventViewTypeEnum.JOINED:
        isFavorited = false;
        favoriteCount = null;
        memberFirstnames = ivent.member_first_names;
        break;
      case IventViewTypeEnum.DEFAULT:
        isFavorited = ivent.is_favorited!;
        favoriteCount = null;
        memberFirstnames = ivent.member_first_names;
        break;
    }

    return {
      iventId: ivent.id,
      iventName: ivent.ivent_name,
      thumbnailUrl: ivent.thumbnail_url,
      locationName: ivent.location!.location_name,
      locationId: ivent.location!.id,
      mapboxId: ivent.location!.mapbox_id,
      creatorId: ivent.creator_id!,
      creatorType: ivent.creator_type!,
      creatorUsername: ivent.creator_name!,
      creatorImageUrl: ivent.creator_image_url,
      description: ivent.description,
      categoryTag: ivent.category_tag!.hobby_name,
      tagNames: ivent.tag_list,
      dates: ivent.date_list,
      googleFormsUrl: ivent.google_forms_url,
      callNumber: ivent.call_number,
      instagramUsername: ivent.instagram_username,
      websiteUrl: ivent.website_url,
      whatsappUrl: ivent.whatsapp_url,
      whatsappNumber: ivent.whatsapp_number,
      isWhatsappUrlPrivate: ivent.is_whatsapp_url_private,
      viewType: ivent.view_type!,
      collabCount,
      collabNames,
      memberCount: ivent.member_count!,
      memberAvatarUrls: ivent.member_avatar_urls!,
      memberFirstnames: memberFirstnames,
      isFavorited,
      favoriteCount,
    };
  }

  async getBannerByIventId(getBannerByIventIdParams: GetBannerByIventIdParams): Promise<GetBannerByIventIdReturn> {
    const { sessionId, iventIds } = getBannerByIventIdParams;

    const queryBuilder = this.dataSource
      .getRepository(Ivent)
      .createQueryBuilder('i')
      .leftJoinAndSelect('i.location', 'l')
      .leftJoinAndSelect('i.creator_page', 'p')
      .leftJoinAndSelect('i.creator_user', 'u')
      .leftJoinAndSelect('i.creator_distributor', 'dis')
      .leftJoin('i.favorited_by', 'uf', 'uf.user_id = :sessionId', { sessionId })
      .addSelect('CASE WHEN uf.user_id IS NOT NULL THEN TRUE ELSE FALSE END', 'is_favorited')
      .where('i.id IN (:...iventIds)', { iventIds });

    const { entities, raw } = await queryBuilder.getRawAndEntities();

    if (entities.length === 0) {
      throw new HttpException('No ivents found.', HttpStatus.NOT_FOUND);
    }

    const transformedIvents = hydrateComputedList(entities, raw, [{ field: 'is_favorited', transformer: stringToBooleanTransformer }]);

    return {
      ivents: transformedIvents.map((ivent) => ({
        iventId: ivent.id,
        iventName: ivent.ivent_name,
        thumbnailUrl: ivent.thumbnail_url,
        locationName: ivent.location!.location_name,
        creatorId: ivent.creator_id!,
        creatorType: ivent.creator_type,
        creatorUsername: ivent.creator_name!,
        creatorImageUrl: ivent.creator_image_url,
        isFavorited: ivent.is_favorited!,
      })),
      iventCount: transformedIvents.length,
    };
  }

  async getLatestIvents(getLatestIventsParams: GetLatestIventsParams): Promise<GetLatestIventsReturn> {
    const { sessionId, limit, page } = getLatestIventsParams;

    const ivents = await this.dataSource
      .getRepository(Ivent)
      .createQueryBuilder('i')
      .leftJoinAndSelect('i.location', 'l')
      .leftJoinAndSelect('i.creator_page', 'p')
      .leftJoinAndSelect('i.creator_user', 'u')
      .leftJoinAndSelect('i.creator_distributor', 'dis')
      .leftJoinAndSelect('i.ivent_dates_aggregated', 'd')
      .leftJoinAndSelect('i.active_session_ivents', 'asi', 'asi.account_id = :sessionId', { sessionId })
      .leftJoinAndSelect('i.member_summary_of_session_squad', 'msoss', 'msoss.user_id = :sessionId', { sessionId })
      .innerJoin('i.ivent_users', 'iu', 'iu.account_id = :sessionId', { sessionId })
      .where("(SELECT MIN(ivent_date) FROM ivent_dates WHERE ivent_id = i.id AND ivent_date >= NOW()) < date_trunc('day', NOW() + INTERVAL '1 day')")
      .andWhere('iu.type = :type', { type: 'member' })
      .orderBy('i.created_at', 'DESC')
      .limit(limit)
      .offset(limit * (page - 1))
      .getMany();

    return {
      ivents: ivents.map((ivent) => ({
        vibeFolderId: 'ivent.vibe_folder_id',
        iventId: ivent.id,
        iventName: ivent.ivent_name,
        thumbnailUrl: ivent.thumbnail_url,
        locationName: ivent.location!.location_name,
        dates: ivent.date_list,
        creatorId: ivent.creator_id!,
        creatorType: ivent.creator_type!,
        creatorUsername: ivent.creator_name!,
        creatorImageUrl: ivent.creator_image_url,
        viewType: ivent.view_type!,
        memberCount: ivent.member_count!,
        memberFirstnames: ivent.member_first_names!,
        memberAvatarUrls: ivent.member_avatar_urls!,
        isFavorited: ivent.is_favorited!,
      })),
      iventCount: ivents.length,
    };
  }

  async getSuggestedImages(getSuggestedImagesParams: GetSuggestedImagesParams): Promise<GetSuggestedImagesReturn> {
    const { sessionId, criterias } = getSuggestedImagesParams;

    if (!criterias || criterias.length === 0) {
      return {
        imageUrls: [],
        imageCount: 0,
      };
    }

    return {
      imageUrls: [],
      imageCount: 0,
    };
  }

  async updateDateByIventId(updateDateByIventIdParams: UpdateDateByIventIdParams): Promise<EmptyReturn> {
    const { sessionId, iventId, newDates } = updateDateByIventIdParams;

    // Check if ivent exists and user has permission to update it
    const ivent = await this.iventRepository.findOne({
      where: { id: iventId },
      relations: ['dates', 'collabs'],
    });

    if (!ivent) {
      throw new HttpException('Ivent not found.', HttpStatus.NOT_FOUND);
    }

    // Check if user is the creator or a collaborator with admin rights
    const isCreator = ivent.creator_user_id === sessionId || ivent.creator_page_id === sessionId;
    const isAdminCollab = ivent.collabs?.some((collab) => (collab.collab_user_id === sessionId || collab.collab_page_id === sessionId) && collab.status === IventCollabStatusEnum.ADMIN);

    if (!isCreator && !isAdminCollab) {
      throw new HttpException('You do not have permission to update this ivent.', HttpStatus.FORBIDDEN);
    }

    // Validate dates
    const validDates = newDates.map((dateStr) => {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) {
        throw new HttpException(`Invalid date format: ${dateStr}`, HttpStatus.BAD_REQUEST);
      }
      return date;
    });

    // Remove existing dates and add new ones
    await this.dataSource.transaction(async (manager) => {
      // Remove existing dates
      await manager.delete(IventDate, { ivent_id: iventId });

      // Add new dates
      const newIventDates = validDates.map((date) => {
        const iventDate = new IventDate();
        iventDate.ivent_id = iventId;
        iventDate.ivent_date = date;
        return iventDate;
      });

      await manager.save(IventDate, newIventDates);
    });

    return {};
  }

  async updateDetailsByIventId(updateDetailsByIventIdParams: UpdateDetailsByIventIdParams): Promise<EmptyReturn> {
    const { sessionId, iventId, newDescription } = updateDetailsByIventIdParams;

    // Check if ivent exists and user has permission to update it
    const ivent = await this.iventRepository.findOne({
      where: { id: iventId },
      relations: ['collabs'],
    });

    if (!ivent) {
      throw new HttpException('Ivent not found.', HttpStatus.NOT_FOUND);
    }

    // Check if user is the creator or a collaborator with admin rights
    const isCreator = ivent.creator_user_id === sessionId || ivent.creator_page_id === sessionId;
    const isAdminCollab = ivent.collabs?.some((collab) => (collab.collab_user_id === sessionId || collab.collab_page_id === sessionId) && collab.status === IventCollabStatusEnum.ADMIN);

    if (!isCreator && !isAdminCollab) {
      throw new HttpException('You do not have permission to update this ivent.', HttpStatus.FORBIDDEN);
    }

    // Update the description
    await this.iventRepository.update(iventId, {
      description: newDescription,
    });

    return {};
  }

  async updateLocationByIventId(updateLocationByIventIdParams: UpdateLocationByIventIdParams): Promise<EmptyReturn> {
    const { sessionId, iventId, newlocationId } = updateLocationByIventIdParams;

    // Check if ivent exists and user has permission to update it
    const ivent = await this.iventRepository.findOne({
      where: { id: iventId },
      relations: ['collabs'],
    });

    if (!ivent) {
      throw new HttpException('Ivent not found.', HttpStatus.NOT_FOUND);
    }

    // Check if user is the creator or a collaborator with admin rights
    const isCreator = ivent.creator_user_id === sessionId || ivent.creator_page_id === sessionId;
    const isAdminCollab = ivent.collabs?.some((collab) => (collab.collab_user_id === sessionId || collab.collab_page_id === sessionId) && collab.status === IventCollabStatusEnum.ADMIN);

    if (!isCreator && !isAdminCollab) {
      throw new HttpException('You do not have permission to update this ivent.', HttpStatus.FORBIDDEN);
    }

    // Verify that the new location exists
    const location = await this.locationRepository.findOne({
      where: { id: newlocationId },
    });

    if (!location) {
      throw new HttpException('Location not found.', HttpStatus.NOT_FOUND);
    }

    // Update the location
    await this.iventRepository.update(iventId, {
      location_id: newlocationId,
    });

    return {};
  }

  async favoriteIventByIventId(favoriteIventByIventIdParams: FavoriteIventByIventIdParams): Promise<EmptyReturn> {
    const { sessionId, iventId } = favoriteIventByIventIdParams;

    // Check if ivent exists
    const ivent = await this.iventRepository.findOne({
      where: { id: iventId },
    });

    if (!ivent) {
      throw new HttpException('Ivent not found.', HttpStatus.NOT_FOUND);
    }

    // Check if already favorited to avoid duplicates
    const existingFavorite = await this.dataSource.query('SELECT id FROM user_favorites WHERE user_id = $1 AND favorited_ivent_id = $2', [sessionId, iventId]);

    if (existingFavorite.length > 0) {
      throw new HttpException('Ivent is already favorited.', HttpStatus.CONFLICT);
    }

    // Add to favorites using the insertQueryBuilder utility
    await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'user_favorites',
        values: {
          user_id: sessionId,
          favorited_ivent_id: iventId,
        },
      }),
    );

    return {};
  }

  async unfavoriteIventByIventId(unfavoriteIventByIventIdParams: UnfavoriteIventByIventIdParams): Promise<EmptyReturn> {
    const { sessionId, iventId } = unfavoriteIventByIventIdParams;

    // Check if ivent exists
    const ivent = await this.iventRepository.findOne({
      where: { id: iventId },
    });

    if (!ivent) {
      throw new HttpException('Ivent not found.', HttpStatus.NOT_FOUND);
    }

    // Check if the favorite exists before trying to delete it
    const existingFavorite = await this.dataSource.query('SELECT id FROM user_favorites WHERE user_id = $1 AND favorited_ivent_id = $2', [sessionId, iventId]);

    if (existingFavorite.length === 0) {
      throw new HttpException('Ivent is not in your favorites.', HttpStatus.NOT_FOUND);
    }

    // Remove from favorites
    const result = await this.dataSource
      .createQueryBuilder()
      .delete()
      .from('user_favorites')
      .where({
        user_id: sessionId,
        favorited_ivent_id: iventId,
      })
      .execute();

    if (result.affected === 0) {
      throw new HttpException('Failed to remove ivent from favorites.', HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return {};
  }

  async getUpcomingIvent(getUpcomingIventParams: GetUpcomingIventParams): Promise<GetUpcomingIventReturn> {
    const { sessionId } = getUpcomingIventParams;

    const ivent = await this.dataSource
      .getRepository(Ivent)
      .createQueryBuilder('i')
      .leftJoinAndSelect('i.location', 'l')
      .leftJoinAndSelect('i.creator_page', 'p')
      .leftJoinAndSelect('i.creator_user', 'u')
      .leftJoinAndSelect('i.creator_distributor', 'dis')
      .leftJoinAndSelect('i.ivent_dates_aggregated', 'd')
      .leftJoinAndSelect('i.active_session_ivents', 'asi', 'asi.account_id = :sessionId', { sessionId })
      .leftJoinAndSelect('i.member_summary_of_session_squad', 'msoss', 'msoss.user_id = :sessionId', { sessionId })
      .innerJoin('i.ivent_users', 'iu', 'iu.account_id = :sessionId', { sessionId })
      .where("(SELECT MIN(ivent_date) FROM ivent_dates WHERE ivent_id = i.id AND ivent_date >= NOW()) BETWEEN NOW() AND date_trunc('day', NOW() + INTERVAL '2 day')")
      .andWhere('iu.type = :type', { type: 'member' })
      .orderBy('i.created_at', 'DESC')
      .limit(1)
      .getOne();

    if (!ivent) {
      throw new HttpException('No upcoming ivent found.', HttpStatus.NOT_FOUND);
    }

    return {
      iventId: ivent.id,
      iventName: ivent.ivent_name,
      dates: ivent.date_list,
      memberCount: ivent.member_count,
      memberFirstnames: ivent.member_first_names,
    };
  }
}
