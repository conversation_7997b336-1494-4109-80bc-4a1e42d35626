import { ConfigService } from '@nestjs/config';
import 'reflect-metadata';
import { DataSource } from 'typeorm';
import { configServiceConfig } from './config/config-service.config';
import { DatabaseConfig } from './config/database.config';
import * as entities from './entities';

const configService = new ConfigService(configServiceConfig);
const dbConfig = configService.get<DatabaseConfig>('database', { infer: true })!;

export const AppDataSource = new DataSource({
  type: dbConfig.type,
  host: dbConfig.host,
  port: dbConfig.port,
  username: dbConfig.username,
  password: dbConfig.password,
  database: dbConfig.database,
  ssl: dbConfig.ssl,
  entities: Object.values(entities),
  migrations: [__dirname + '/migrations/*.{ts,js}'],
  migrationsRun: true,
});
