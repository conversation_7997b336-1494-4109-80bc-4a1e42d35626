import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { configServiceConfig } from './config/config-service.config';
import { typeOrmConfigAsync } from './config/typeorm.config';
import { AppLoggerMiddleware } from './middlewares/app-logger-middleware';
import { TokenMiddleware } from './middlewares/token-middleware';
import { AuthModule } from './modules/auth/auth.module';
import { AwsS3Module } from './modules/aws-s3/aws-s3.module';
import { CommentsModule } from './modules/comments/comments.module';
import { GroupMembershipsModule } from './modules/group-memberships/group-memberships.module';
import { GroupsModule } from './modules/groups/groups.module';
import { HobbiesModule } from './modules/hobbies/hobbies.module';
import { HomeModule } from './modules/home/<USER>';
import { IventCollabsModule } from './modules/ivent-collabs/ivent-collabs.module';
import { IventsModule } from './modules/ivents/ivents.module';
import { LocationsModule } from './modules/locations/locations.module';
import { MapboxModule } from './modules/mapbox/mapbox.module';
import { MemoriesModule } from './modules/memories/memories.module';
import { MemoryFoldersModule } from './modules/memory-folders/memory-folders.module';
import { NotificationsModule } from './modules/notifications/notifications.module';
import { PageBlacklistsModule } from './modules/page-blacklists/page-blacklists.module';
import { PageMembershipsModule } from './modules/page-memberships/page-memberships.module';
import { PagesModule } from './modules/pages/pages.module';
import { SquadMembershipsModule } from './modules/squad-memberships/squad-memberships.module';
import { SquadsModule } from './modules/squads/squads.module';
import { UniversitiesModule } from './modules/universities/universities.module';
import { UserRelationshipsModule } from './modules/user-relationships/user-relationships.module';
import { UsersModule } from './modules/users/users.module';
import { VibeFoldersModule } from './modules/vibe-folders/vibe-folders.module';
import { VibesModule } from './modules/vibes/vibes.module';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true, ...configServiceConfig }),
    TypeOrmModule.forRootAsync(typeOrmConfigAsync),
    AuthModule,
    AwsS3Module,
    CommentsModule,
    GroupMembershipsModule,
    GroupsModule,
    HobbiesModule,
    HomeModule,
    IventCollabsModule,
    IventsModule,
    LocationsModule,
    MapboxModule,
    MemoriesModule,
    MemoryFoldersModule,
    NotificationsModule,
    PageBlacklistsModule,
    PageMembershipsModule,
    PagesModule,
    SquadMembershipsModule,
    SquadsModule,
    UniversitiesModule,
    UserRelationshipsModule,
    UsersModule,
    VibeFoldersModule,
    VibesModule,
  ],
  controllers: [AppController],
  providers: [],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenMiddleware).forRoutes('*');
    consumer.apply(AppLoggerMiddleware).forRoutes('*');
  }
}
