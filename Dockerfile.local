# Multi-stage Dockerfile for local environment
FROM node:22-alpine AS local

# Install system dependencies for native builds
RUN apk add --no-cache \
    curl \
    postgresql-client \
    bash \
    git \
    openssh-client \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nestjs -u 1001

WORKDIR /app

# Copy package files first
COPY package*.json ./

# Install ALL dependencies (including dev)
RUN npm ci --include=dev && npm cache clean --force

# Copy full source code
COPY . .

# Ensure correct ownership
RUN chown -R nestjs:nodejs /app

USER nestjs

# Expose NestJS default port
EXPOSE 3000

# Start with hot-reload (ts-node / Nest CLI)
CMD ["npm", "run", "start:dev"]
