# Database Connectivity Troubleshooting Guide

## Overview

This document provides a comprehensive guide for resolving database connectivity issues in a NestJS application deployed on AWS ECS Fargate with RDS PostgreSQL. The issues documented here were encountered during deployment and successfully resolved through systematic troubleshooting.

## Problem Summary

The application was experiencing multiple interconnected issues preventing successful deployment and database connectivity:

1. **SSL Certificate Validation Errors**: Application failing to connect to RDS PostgreSQL due to self-signed certificate issues
2. **Docker Build Path Issues**: ECS tasks failing with "Cannot find module" errors due to incorrect file paths
3. **Platform Compatibility Issues**: Docker images incompatible with ECS Fargate's linux/amd64 platform requirement

## Root Cause Analysis

### 1. SSL Certificate Validation Errors

**Error Messages:**
```
self-signed certificate in certificate chain
DEPTH_ZERO_SELF_SIGNED_CERT
```

**Root Cause:**
- AWS RDS PostgreSQL requires SSL connections for security
- RDS uses self-signed certificates that Node.js rejects by default
- Inconsistent SSL configuration between TypeORM config files
- The application had different SSL settings in `typeorm.config.ts` and `data-source.ts`

**Technical Details:**
- Node.js `rejectUnauthorized` defaults to `true`, causing certificate validation failures
- AWS RDS certificates are not signed by a trusted CA, requiring explicit acceptance
- Environment-dependent SSL configuration created inconsistencies

### 2. Docker Build Path Issues

**Error Messages:**
```
Error: Cannot find module '/app/dist/main.js'
MODULE_NOT_FOUND
```

**Root Cause:**
- NestJS build process outputs compiled files to `dist/src/` directory structure
- Dockerfile CMD was pointing to incorrect path (`dist/main.js` instead of `dist/src/main.js`)
- Multi-stage Docker build was copying files correctly but referencing wrong execution path

**Technical Details:**
- NestJS TypeScript compilation preserves source directory structure in output
- The `nest build` command creates `dist/src/main.js`, not `dist/main.js`
- Docker CMD instruction was using legacy path assumption

### 3. Platform Compatibility Issues

**Error Messages:**
```
CannotPullContainerError: pull image manifest has been retried 7 time(s): 
image Manifest does not contain descriptor matching platform 'linux/amd64'
```

**Root Cause:**
- Docker image built on ARM64 Mac (Apple Silicon) architecture
- AWS ECS Fargate requires linux/amd64 platform compatibility
- Default Docker build creates images for host platform only

**Technical Details:**
- ECS Fargate only supports x86_64 (amd64) architecture
- Multi-architecture image manifests were missing amd64 variant
- Docker buildx required for cross-platform builds

## Technical Solutions

### 1. SSL Configuration Fix

**Files Modified:**
- `src/config/typeorm.config.ts`
- `src/data-source.ts`

**Before:**
```typescript
// typeorm.config.ts - Environment dependent SSL
ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: true } : false

// data-source.ts - Different SSL logic
ssl: process.env.NODE_ENV !== 'local' ? true : false
```

**After:**
```typescript
// typeorm.config.ts - Consistent SSL for AWS RDS
ssl: {
  rejectUnauthorized: false, // AWS RDS uses self-signed certificates
},

// data-source.ts - Aligned SSL configuration
const sslConfig = isLocal
  ? false // Local development with Docker doesn't need SSL
  : {
      rejectUnauthorized: false, // AWS RDS uses self-signed certificates
    };
```

### 2. Docker Build Path Fix

**File Modified:**
- `Dockerfile`

**Before:**
```dockerfile
CMD ["node", "dist/main.js"]
```

**After:**
```dockerfile
CMD ["node", "dist/src/main.js"]
```

### 3. Platform Compatibility Fix

**Build Command Updated:**
```bash
# Before - Default platform build
docker build -t image-name .

# After - Cross-platform build
docker buildx build --platform linux/amd64 -t image-name .
```

**ECS Task Definition Updated:**
- Created new task definition revision with corrected image
- Updated ECS service to use new task definition
- Forced new deployment to apply changes

## Implementation Steps

### Step 1: Fix SSL Configuration
1. Updated TypeORM configuration to use consistent SSL settings
2. Modified data source configuration to align with TypeORM config
3. Added debug logging for connection parameters
4. Committed SSL configuration changes

### Step 2: Fix Docker Build Path
1. Identified correct NestJS build output structure
2. Updated Dockerfile CMD to use correct path
3. Rebuilt Docker image with fixed configuration
4. Tested image locally to verify fix

### Step 3: Fix Platform Compatibility
1. Rebuilt image using `docker buildx` with `--platform linux/amd64`
2. Pushed platform-compatible image to ECR
3. Created new ECS task definition with updated image
4. Updated ECS service to use new task definition

### Step 4: Deploy and Verify
1. Forced new ECS deployment
2. Monitored deployment progress and task health
3. Verified application startup and database connectivity
4. Tested health endpoints through load balancer

## Verification Steps

### 1. SSL Connection Verification
- **Method**: Analyzed ECS task logs for database connection success
- **Result**: No SSL certificate errors, successful database queries
- **Evidence**: Application logs showing successful TypeORM initialization

### 2. Application Startup Verification
- **Method**: Monitored ECS task logs for successful NestJS startup
- **Result**: All modules loaded, routes mapped successfully
- **Evidence**: "Nest application successfully started" log message

### 3. Health Check Verification
- **Method**: Tested health endpoint through load balancer
- **Command**: `curl http://load-balancer-dns/health`
- **Result**: HTTP 200 response
- **Evidence**: Consistent health check passes in ECS service events

### 4. Database Query Verification
- **Method**: Observed database queries in application logs
- **Result**: Successful schema creation and extension installation
- **Evidence**: PostgreSQL queries executing without errors

### 5. ECS Service Verification
- **Method**: Checked ECS service deployment status
- **Result**: Service running with desired task count
- **Evidence**: Target group registration and health check passes

## Lessons Learned

### 1. SSL Configuration Consistency
- **Lesson**: Maintain consistent SSL configuration across all database connection files
- **Prevention**: Use centralized configuration management for database settings
- **Best Practice**: Document SSL requirements for different environments clearly

### 2. Docker Build Path Validation
- **Lesson**: Verify file paths in Docker images match runtime expectations
- **Prevention**: Test Docker images locally before deployment
- **Best Practice**: Use multi-stage builds with explicit file copying and verification

### 3. Platform Architecture Awareness
- **Lesson**: Consider target platform architecture when building Docker images
- **Prevention**: Use CI/CD pipelines with platform-specific build configurations
- **Best Practice**: Always specify target platform in Docker build commands for production

### 4. Systematic Troubleshooting Approach
- **Lesson**: Address issues in logical order (connectivity → application → infrastructure)
- **Prevention**: Implement comprehensive logging and monitoring
- **Best Practice**: Document error patterns and solutions for future reference

### 5. Environment Parity
- **Lesson**: Ensure development and production environments have similar configurations
- **Prevention**: Use infrastructure as code and consistent environment setup
- **Best Practice**: Test deployment process in staging environment first

## Future Prevention Strategies

1. **Automated Testing**: Implement database connectivity tests in CI/CD pipeline
2. **Configuration Validation**: Add startup checks for database connection parameters
3. **Platform-Specific Builds**: Configure CI/CD to build for target platform automatically
4. **Monitoring**: Set up alerts for database connection failures and SSL errors
5. **Documentation**: Maintain up-to-date deployment and troubleshooting guides

## Quick Reference Commands

### Database Connectivity Test
```bash
# Run connectivity test in ECS container
aws ecs execute-command --cluster cluster-name --task task-id --container app \
  --command "node scripts/test-db-connectivity.js"
```

### ECS Service Monitoring
```bash
# Check service status
aws ecs describe-services --cluster cluster-name --services service-name

# View recent service events
aws ecs describe-services --cluster cluster-name --services service-name \
  --query 'services[0].events[0:5].[createdAt,message]' --output table
```

### Docker Platform Build
```bash
# Build for ECS Fargate
docker buildx build --platform linux/amd64 -t image-name .

# Push to ECR
docker push ecr-repository-uri:tag
```

### Health Check Verification
```bash
# Test application health
curl -s -o /dev/null -w "%{http_code}" "http://load-balancer-dns/health"
```

---

**Document Version**: 1.0  
**Last Updated**: September 24, 2025  
**Status**: Resolved - All issues successfully fixed and verified
