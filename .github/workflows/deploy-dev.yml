name: Deploy to Development

on:
  push:
    branches: [develop]
  workflow_dispatch:

env:
  AWS_REGION: eu-central-1
  ENVIRONMENT: dev

jobs:
  deploy-infrastructure:
    name: Deploy Infrastructure
    runs-on: ubuntu-latest
    environment: development

    outputs:
      ecr-repository: ${{ steps.terraform.outputs.ecr_repository_url }}
      load_balancer_dns: ${{ steps.terraform.outputs.load_balancer_dns }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ~1.0
          terraform_wrapper: false

      - name: Terraform Init
        working-directory: ./terraform
        run: |
          terraform init \
            -backend-config="bucket=ivent-terraform-state-${{ env.ENVIRONMENT }}-${{ env.AWS_REGION }}" \
            -backend-config="key=terraform.tfstate" \
            -backend-config="region=${{ env.AWS_REGION }}"

      - name: Terraform Plan
        working-directory: ./terraform
        run: |
          terraform plan \
            -var-file="environments/${{ env.ENVIRONMENT }}/terraform.tfvars" \
            -var="db_password=${{ secrets.DB_PASSWORD }}" \
            -var="jwt_secret=${{ secrets.JWT_SECRET }}" \
            -var="mapbox_access_token=${{ secrets.MAPBOX_ACCESS_TOKEN }}" \
            -out=tfplan

      - name: Terraform Apply
        working-directory: ./terraform
        run: terraform apply -auto-approve tfplan

      - name: Get Terraform Outputs
        id: terraform
        working-directory: ./terraform
        run: |
          echo "ecr_repository_url=$(terraform output -raw ecr_repository_url)" >> $GITHUB_OUTPUT
          echo "load_balancer_dns=$(terraform output -raw load_balancer_dns_name)" >> $GITHUB_OUTPUT

  build-and-deploy:
    name: Build and Deploy Application
    runs-on: ubuntu-latest
    needs: deploy-infrastructure
    environment: development

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: |
            ${{ needs.deploy-infrastructure.outputs.ecr-repository }}:latest
            ${{ needs.deploy-infrastructure.outputs.ecr-repository }}:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Update ECS service
        run: |
          aws ecs update-service \
            --cluster "ivent-api-${{ env.ENVIRONMENT }}-cluster" \
            --service "ivent-api-${{ env.ENVIRONMENT }}-service" \
            --force-new-deployment \
            --region ${{ env.AWS_REGION }}

      - name: Wait for ECS deployment to complete
        continue-on-error: true
        run: |
          # Increase waiter timeout to allow longer rollouts (e.g., cold start, new capacity, slow image pull)
          aws ecs wait services-stable \
            --cluster "ivent-api-${{ env.ENVIRONMENT }}-cluster" \
            --services "ivent-api-${{ env.ENVIRONMENT }}-service" \
            --region ${{ env.AWS_REGION }} \
            --max-attempts 60 \
            --delay 20 || echo "ECS waiter timed out; collecting diagnostics next."

      - name: Diagnose ECS deployment (status, events, tasks)
        if: always()
        run: |
          CLUSTER="ivent-api-${{ env.ENVIRONMENT }}-cluster"
          SERVICE="ivent-api-${{ env.ENVIRONMENT }}-service"
          REGION="${{ env.AWS_REGION }}"

          echo "ECS primary deployment rollout state:"
          aws ecs describe-services \
            --cluster "$CLUSTER" \
            --services "$SERVICE" \
            --region "$REGION" \
            --query 'services[0].deployments[?status==`PRIMARY`].[rolloutState,rolloutStateReason]' \
            --output table || true

          echo "Recent ECS service events:"
          aws ecs describe-services \
            --cluster "$CLUSTER" \
            --services "$SERVICE" \
            --region "$REGION" \
            --query 'services[0].events[0:15].[createdAt,message]' \
            --output table || true

          STATUS=$(aws ecs describe-services \
            --cluster "$CLUSTER" \
            --services "$SERVICE" \
            --region "$REGION" \
            --query 'services[0].deployments[?status==`PRIMARY`].rolloutState' \
            --output text || echo "UNKNOWN")

          if [ "$STATUS" != "COMPLETED" ]; then
            echo "Stopped tasks (last 10) for diagnostics:"
            TASK_ARNS=$(aws ecs list-tasks \
              --cluster "$CLUSTER" \
              --service-name "$SERVICE" \
              --region "$REGION" \
              --desired-status STOPPED \
              --max-results 10 \
              --output text \
              --query 'taskArns' || true)

            if [ -n "$TASK_ARNS" ]; then
              aws ecs describe-tasks \
                --cluster "$CLUSTER" \
                --tasks $TASK_ARNS \
                --region "$REGION" \
                --query 'tasks[].{taskArn:taskArn,lastStatus:lastStatus,stopCode:stopCode,stoppedReason:stoppedReason,containers:containers[].{name:name,exitCode:exitCode,reason:reason}}' \
                --output json || true
            else
              echo "No stopped tasks found."
            fi

            echo "ECS service has not stabilized within the waiter window. Failing the job."
            exit 1
          fi

      - name: Get deployment status
        run: |
          aws ecs describe-services \
            --cluster "ivent-api-${{ env.ENVIRONMENT }}-cluster" \
            --services "ivent-api-${{ env.ENVIRONMENT }}-service" \
            --region ${{ env.AWS_REGION }} \
            --query 'services[0].deployments[0].status' \
            --output text

  health-check:
    name: Health Check
    runs-on: ubuntu-latest
    needs: [deploy-infrastructure, build-and-deploy]

    steps:
      - name: Comprehensive health check
        run: |
          LOAD_BALANCER_DNS="${{ needs.deploy-infrastructure.outputs.load_balancer_dns }}"

          for i in {1..15}; do
            if curl -f "http://${LOAD_BALANCER_DNS}/health"; then
              echo "Health check passed!"
              exit 0
            fi
            echo "Health check attempt $i failed, retrying in 30 seconds..."
            sleep 30
          done

          echo "Health check failed after 15 attempts"
          exit 1

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-infrastructure, build-and-deploy, health-check]
    if: always()

    steps:
      - name: Notify success
        if: needs.health-check.result == 'success'
        run: |
          echo "✅ Development deployment successful!"
          echo "Application URL: http://${{ needs.deploy-infrastructure.outputs.load_balancer_dns }}"
          echo "API Documentation: http://${{ needs.deploy-infrastructure.outputs.load_balancer_dns }}/api"

      - name: Notify failure
        if: failure()
        run: |
          echo "❌ Development deployment failed!"
          echo "Check the logs for more details."
