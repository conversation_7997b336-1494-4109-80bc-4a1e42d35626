name: CI Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '24'
  NODE_ENV: test
  POSTGRES_USER: test_user
  POSTGRES_PASSWORD: test_password
  POSTGRES_DB: test_db

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:17
        env:
          POSTGRES_USER: ${{ env.POSTGRES_USER }}
          POSTGRES_PASSWORD: ${{ env.POSTGRES_PASSWORD }}
          POSTGRES_DB: ${{ env.POSTGRES_DB }}
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Run type checking
        run: npx tsc --noEmit

      # - name: Run unit tests
      #   run: npm run test
      #   env:
      #     NODE_ENV: ${{ env.NODE_ENV }}
      #     POSTGRES_HOST: 127.0.0.1
      #     POSTGRES_PORT: 5432
      #     POSTGRES_USER: ${{ env.POSTGRES_USER }}
      #     POSTGRES_PASSWORD: ${{ env.POSTGRES_PASSWORD }}
      #     POSTGRES_DB: ${{ env.POSTGRES_DB }}

      # - name: Run e2e tests
      #   run: npm run test:e2e
      #   env:
      #     NODE_ENV: ${{ env.NODE_ENV }}
      #     POSTGRES_HOST: 127.0.0.1
      #     POSTGRES_PORT: 5432
      #     POSTGRES_USER: ${{ env.POSTGRES_USER }}
      #     POSTGRES_PASSWORD: ${{ env.POSTGRES_PASSWORD }}
      #     POSTGRES_DB: ${{ env.POSTGRES_DB }}

      # - name: Generate test coverage
      #   run: npm run test:cov

      # - name: Upload coverage to Codecov
      #   uses: codecov/codecov-action@v3
      #   with:
      #     file: ./coverage/lcov.info
      #     flags: unittests
      #     name: codecov-umbrella

  build:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: test

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: false
          tags: ivent-api:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: test

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run security audit
        run: npm audit --audit-level=critical
