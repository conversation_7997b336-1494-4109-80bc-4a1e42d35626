
```sh
echo "ECR_REPO=$ECR_REPO"
docker build -t ivent-api:latest .
docker tag ivent-api:latest "${ECR_REPO}:latest"
docker push "${ECR_REPO}:latest"

docker stop ivent-api-aws && docker rm ivent-api-aws
docker run -d --env-file .env.aws --name ivent-api-aws -p 8080:8080 ivent-api:latest
docker exec -it ivent-api-aws sh

echo "POSTGRES_USER=$POSTGRES_USER"
echo "POSTGRES_PASSWORD=$POSTGRES_PASSWORD"
echo "POSTGRES_HOST=$POSTGRES_HOST"
echo "POSTGRES_PORT=$POSTGRES_PORT"
echo "POSTGRES_DB=$POSTGRES_DB"
echo "postgresql://$POSTGRES_USER:$POSTGRES_PASSWORD@$POSTGRES_HOST:$POSTGRES_PORT/$POSTGRES_DB"
psql "postgresql://$POSTGRES_USER:$POSTGRES_PASSWORD@$POSTGRES_HOST:$POSTGRES_PORT/$POSTGRES_DB" -c "SELECT 1;"
```
