```sh
POSTGRES_HOST=ivent-api-dev-db.czy6se0si8ii.eu-central-1.rds.amazonaws.com
POSTGRES_PORT=5432
POSTGRES_USER=ivent_admin
POSTGRES_PASSWORD=QDWez3jJVE4Gpmu8
POSTGRES_DB=ivent_api_dev
SSLMODE=verify-full
SSLROOTCERT=eu-central-1-bundle.pem

psql "postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}?sslmode=${SSLMODE}"

echo "postgresql://${POSTGRES_USER}:my_secret_password@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}?sslmode=${SSLMODE}&sslrootcert=${SSLROOTCERT}"
psql "postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}?sslmode=${SSLMODE}&sslrootcert=${SSLROOTCERT}"

nc -vz $POSTGRES_HOST $POSTGRES_PORT

telnet $POSTGRES_HOST $POSTGRES_PORT
```
