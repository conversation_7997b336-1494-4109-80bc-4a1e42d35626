#!/bin/bash

# Terraform to Neo4j Graph Analyzer Wrapper Script
# This script provides a convenient way to analyze Terraform infrastructure
# and optionally import the results into Neo4j

# Source utility functions and add missing ones
source "$(dirname "$0")/../utils.sh"

# Additional utility functions for this script
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Default values
TERRAFORM_DIR="terraform"
OUTPUT_FILE="scripts/neo4j/output/terraform_graph.cypher"
VERBOSE=""
IMPORT_TO_NEO4J=false
NEO4J_URI="bolt://localhost:7687"
NEO4J_USER=""  # Now required parameter
NEO4J_PASSWORD=""
BATCH_SIZE=100
NO_TRANSACTIONS=""
FORCE_CLEANUP=""
DOCKER_CONTAINER=""  # Optional, no default - determines Docker vs local mode

# Function to show usage
show_usage() {
    cat << EOF
Terraform to Neo4j Graph Analyzer

USAGE:
    $0 [OPTIONS]

OPTIONS:
    -d, --terraform-dir DIR     Terraform directory (default: terraform)
    -o, --output FILE          Output Cypher file (default: scripts/neo4j/output/terraform_graph.cypher)
    -v, --verbose              Enable verbose output
    -i, --import               Import results into Neo4j after generation
    --docker-container NAME    Docker container name (if provided, uses Docker mode)
    --neo4j-uri URI            Neo4j URI for local mode (default: bolt://localhost:7687)
    --neo4j-user USER          Neo4j username (required if --import is used)
    --neo4j-password PASS      Neo4j password (required if --import is used)
    --batch-size SIZE          Batch size for Cypher operations (default: 100)
    --no-transactions          Disable transaction wrapping for batch operations
    --force-cleanup            Force cleanup of existing data before import
    -h, --help                 Show this help message

EXAMPLES:
    # Basic analysis
    $0

    # Analyze custom directory with verbose output
    $0 -d /path/to/terraform -v

    # Analyze and import to Docker container
    $0 -i --docker-container neo4j-visualizer --neo4j-user neo4j --neo4j-password mypassword

    # Analyze and import to local Neo4j with custom batch size
    $0 -i --neo4j-user neo4j --neo4j-password mypassword --batch-size 50

    # Custom output file with optimized batching and force cleanup
    $0 -o my_infrastructure.cypher --batch-size 200 --no-transactions --force-cleanup
EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--terraform-dir)
            TERRAFORM_DIR="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_FILE="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE="--verbose"
            shift
            ;;
        -i|--import)
            IMPORT_TO_NEO4J=true
            shift
            ;;
        --docker-container)
            DOCKER_CONTAINER="$2"
            shift 2
            ;;
        --neo4j-uri)
            NEO4J_URI="$2"
            shift 2
            ;;
        --neo4j-user)
            NEO4J_USER="$2"
            shift 2
            ;;
        --neo4j-password)
            NEO4J_PASSWORD="$2"
            shift 2
            ;;
        --batch-size)
            BATCH_SIZE="$2"
            shift 2
            ;;
        --no-transactions)
            NO_TRANSACTIONS="--no-transactions"
            shift
            ;;
        --force-cleanup)
            FORCE_CLEANUP="--force-cleanup"
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is required but not installed"
    exit 1
fi

# Check if the Terraform directory exists
if [[ ! -d "$TERRAFORM_DIR" ]]; then
    print_error "Terraform directory '$TERRAFORM_DIR' does not exist"
    exit 1
fi

# Check if the analyzer script exists
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ANALYZER_SCRIPT="$SCRIPT_DIR/terraform_to_neo4j.py"

if [[ ! -f "$ANALYZER_SCRIPT" ]]; then
    print_error "Analyzer script not found at $ANALYZER_SCRIPT"
    exit 1
fi

# Function to check Docker container status
check_docker_container() {
    local container_name="$1"

    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        return 1
    fi

    if ! docker ps --format "table {{.Names}}" | grep -q "^${container_name}$"; then
        print_error "Docker container '$container_name' is not running"
        print_info "Please start the container first: docker start $container_name"
        return 1
    fi

    print_success "Docker container '$container_name' is running"
    return 0
}

# Function to check Neo4j readiness in Docker container
check_neo4j_readiness() {
    local container_name="$1"
    local neo4j_user="$2"
    local neo4j_password="$3"
    local max_attempts=30
    local attempt=1

    print_info "Checking Neo4j readiness in container '$container_name'..."

    while [ $attempt -le $max_attempts ]; do
        if docker exec "$container_name" cypher-shell -a bolt://localhost:7687 -u "$neo4j_user" -p "$neo4j_password" "RETURN 1 as test;" >/dev/null 2>&1; then
            print_success "Neo4j is ready in container '$container_name'"
            return 0
        fi

        print_info "Attempt $attempt/$max_attempts: Neo4j not ready yet, waiting..."
        sleep 2
        attempt=$((attempt + 1))
    done

    print_error "Neo4j failed to become ready in container '$container_name' after $max_attempts attempts"
    return 1
}

# Function to execute Cypher file in Docker container
execute_cypher_in_docker() {
    local container_name="$1"
    local cypher_file="$2"
    local neo4j_user="$3"
    local neo4j_password="$4"

    print_info "Executing Cypher file in Docker container '$container_name'..."

    # Check Neo4j readiness first
    if ! check_neo4j_readiness "$container_name" "$neo4j_user" "$neo4j_password"; then
        return 1
    fi

    # Copy the Cypher file to the container
    if ! docker cp "$cypher_file" "$container_name:/tmp/terraform_graph.cypher"; then
        print_error "Failed to copy Cypher file to Docker container"
        return 1
    fi

    # Execute the Cypher file using cypher-shell inside the container with proper address
    print_info "Executing Cypher queries..."
    if docker exec "$container_name" cypher-shell -a bolt://localhost:7687 -u "$neo4j_user" -p "$neo4j_password" -f /tmp/terraform_graph.cypher; then
        print_success "Successfully executed Cypher queries in Docker container"

        # Clean up the temporary file
        docker exec "$container_name" rm -f /tmp/terraform_graph.cypher
        return 0
    else
        print_error "Failed to execute Cypher queries in Docker container"

        # Show Neo4j logs for debugging
        print_info "Checking Neo4j logs for errors..."
        docker exec "$container_name" tail -10 /var/lib/neo4j/logs/neo4j.log

        # Clean up the temporary file even on failure
        docker exec "$container_name" rm -f /tmp/terraform_graph.cypher
        return 1
    fi
}

# Check if python-hcl2 is installed
if ! python3 -c "import hcl2" 2>/dev/null; then
    print_warning "python-hcl2 package not found. Installing..."
    if ! python3 -m pip install python-hcl2; then
        print_error "Failed to install python-hcl2. Please install manually: pip install python-hcl2"
        exit 1
    fi
    print_success "python-hcl2 installed successfully"
fi

# Create output directory if it doesn't exist
OUTPUT_DIR=$(dirname "$OUTPUT_FILE")
if [[ ! -d "$OUTPUT_DIR" ]]; then
    mkdir -p "$OUTPUT_DIR"
    print_info "Created output directory: $OUTPUT_DIR"
fi

# Run the analyzer
print_info "Starting Terraform infrastructure analysis..."
print_info "Terraform directory: $TERRAFORM_DIR"
print_info "Output file: $OUTPUT_FILE"
print_info "Batch size: $BATCH_SIZE"

# Add cypher-shell compatibility for Docker mode
CYPHER_SHELL_COMPATIBLE=""
if [[ -n "$DOCKER_CONTAINER" ]]; then
    CYPHER_SHELL_COMPATIBLE="--cypher-shell-compatible"
    print_info "Docker mode detected: Using cypher-shell compatible output"
fi

ANALYZER_ARGS="--terraform-dir $TERRAFORM_DIR --output $OUTPUT_FILE --batch-size $BATCH_SIZE $VERBOSE $NO_TRANSACTIONS $FORCE_CLEANUP $CYPHER_SHELL_COMPATIBLE"

if python3 "$ANALYZER_SCRIPT" $ANALYZER_ARGS; then
    print_success "Analysis completed successfully!"
    print_info "Generated optimized Cypher queries saved to: $OUTPUT_FILE"
else
    print_error "Analysis failed"
    exit 1
fi

# Import to Neo4j if requested
if [[ "$IMPORT_TO_NEO4J" == true ]]; then
    # Validate required parameters
    if [[ -z "$NEO4J_USER" ]]; then
        print_error "Neo4j username is required for import. Use --neo4j-user option"
        exit 1
    fi

    if [[ -z "$NEO4J_PASSWORD" ]]; then
        print_error "Neo4j password is required for import. Use --neo4j-password option"
        exit 1
    fi

    # Determine mode based on docker-container parameter
    if [[ -n "$DOCKER_CONTAINER" ]]; then
        # Docker-based import
        print_info "Using Docker container for Neo4j import..."
        print_info "Docker container: $DOCKER_CONTAINER"
        print_info "Neo4j User: $NEO4J_USER"

        if check_docker_container "$DOCKER_CONTAINER"; then
            if execute_cypher_in_docker "$DOCKER_CONTAINER" "$OUTPUT_FILE" "$NEO4J_USER" "$NEO4J_PASSWORD"; then
                print_success "Successfully imported to Neo4j via Docker!"
            else
                print_error "Failed to import to Neo4j via Docker"
                exit 1
            fi
        else
            exit 1
        fi
    else
        # Local Neo4j import
        if command -v cypher-shell &> /dev/null; then
            print_info "Importing to local Neo4j..."
            print_info "Neo4j URI: $NEO4J_URI"
            print_info "Neo4j User: $NEO4J_USER"

            if cypher-shell -a "$NEO4J_URI" -u "$NEO4J_USER" -p "$NEO4J_PASSWORD" -f "$OUTPUT_FILE"; then
                print_success "Successfully imported to Neo4j!"
            else
                print_error "Failed to import to Neo4j"
                exit 1
            fi
        else
            print_warning "cypher-shell not found. Please import manually:"
            print_info "1. Start Neo4j database"
            print_info "2. Open Neo4j Browser (http://localhost:7474)"
            print_info "3. Copy and paste the contents of '$OUTPUT_FILE'"
            print_info "4. Execute the queries"
        fi
    fi
fi

print_success "All operations completed successfully!"

# Show next steps
echo
if [[ "$IMPORT_TO_NEO4J" == true ]]; then
    print_info "Import completed! You can now:"
    echo "  1. Open Neo4j Browser to visualize your infrastructure"
    if [[ -n "$DOCKER_CONTAINER" ]]; then
        echo "     - For Docker: Check your container's exposed ports"
        echo "     - Typically: http://localhost:7474"
    else
        echo "     - Local: http://localhost:7474"
    fi
    echo "  2. Use the example queries in scripts/neo4j/terraform_neo4j_queries.cypher"
    echo "  3. Explore relationships and dependencies in your infrastructure"
else
    print_info "Next steps:"
    echo "  1. Start your Neo4j database if not already running"
    echo "  2. Import the generated Cypher file: $OUTPUT_FILE"
    if [[ -n "$DOCKER_CONTAINER" ]]; then
        echo "     - For Docker: Use --import --docker-container NAME flags"
    else
        echo "     - For local: Use --import flag or manual import"
    fi
    echo "  3. Open Neo4j Browser to visualize your infrastructure"
    echo "  4. Use the example queries in scripts/neo4j/terraform_neo4j_queries.cypher"
fi
echo
print_info "Performance optimizations applied:"
echo "  - Batch size: $BATCH_SIZE"
echo "  - Transactions: $([ -n "$NO_TRANSACTIONS" ] && echo "disabled" || echo "enabled")"
echo "  - UNWIND operations: enabled for better performance"
echo
print_info "For more information, see scripts/neo4j/README_terraform_neo4j.md"
