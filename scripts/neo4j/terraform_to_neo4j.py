#!/usr/bin/env python3
"""
Terraform to Neo4j Graph Analyzer

This script analyzes Terraform infrastructure code and generates Neo4j Cypher queries
to create a visual graph database representation of the infrastructure.

Features:
- Parses HCL (HashiCorp Configuration Language) syntax
- Extracts modules, resources, data sources, variables, outputs, and locals
- Detects dependencies and relationships
- Generates executable Neo4j Cypher queries
- Handles complex Terraform constructs (count, for_each, dynamic blocks)

Usage:
    python scripts/terraform_to_neo4j.py [--terraform-dir terraform/] [--output output.cypher]
"""

import argparse
import json
import os
import re
import sys
from collections import defaultdict
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

try:
    import hcl2
except ImportError:
    print(
        "Error: python-hcl2 package is required. Install with: pip install python-hcl2"
    )
    sys.exit(1)


@dataclass
class TerraformNode:
    """Represents a node in the Terraform graph"""

    id: str
    type: str  # module, resource, data, variable, output, local
    name: str
    source_file: str
    properties: Dict[str, Any] = field(default_factory=dict)
    labels: List[str] = field(default_factory=list)


@dataclass
class TerraformRelationship:
    """Represents a relationship between Terraform nodes"""

    from_node: str
    to_node: str
    relationship_type: str
    properties: Dict[str, Any] = field(default_factory=dict)


class TerraformAnalyzer:
    """Analyzes Terraform files and builds a graph representation"""

    def __init__(self, terraform_dir: str = "terraform"):
        self.terraform_dir = Path(terraform_dir)
        self.nodes: Dict[str, TerraformNode] = {}
        self.relationships: List[TerraformRelationship] = []
        self.variable_references: Dict[str, Set[str]] = defaultdict(set)
        self.module_calls: Dict[str, Dict[str, Any]] = {}

    def analyze(self) -> Tuple[Dict[str, TerraformNode], List[TerraformRelationship]]:
        """Main analysis method"""
        print(f"Analyzing Terraform files in {self.terraform_dir}")

        # Find all Terraform files
        tf_files = self._find_terraform_files()
        print(f"Found {len(tf_files)} Terraform files")

        # Parse each file
        for tf_file in tf_files:
            try:
                self._parse_terraform_file(tf_file)
            except Exception as e:
                print(f"Warning: Failed to parse {tf_file}: {e}")
                continue

        # Build relationships
        self._build_relationships()

        return self.nodes, self.relationships

    def _find_terraform_files(self) -> List[Path]:
        """Find all Terraform files recursively"""
        tf_files = []
        for pattern in ["*.tf", "*.tfvars"]:
            tf_files.extend(self.terraform_dir.rglob(pattern))

        # Also look for tfstate files
        tf_files.extend(self.terraform_dir.rglob("*.tfstate"))

        return sorted(tf_files)

    def _parse_terraform_file(self, file_path: Path):
        """Parse a single Terraform file"""
        print(f"Parsing {file_path}")

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Handle different file types
            if file_path.suffix == ".tfstate":
                self._parse_tfstate_file(file_path, content)
                return
            elif file_path.suffix == ".tfvars":
                self._parse_tfvars_file(file_path, content)
                return

            # Parse HCL content
            parsed = hcl2.loads(content)
            self._extract_terraform_elements(file_path, parsed)

        except Exception as e:
            print(f"Error parsing {file_path}: {e}")
            raise

    def _parse_tfstate_file(self, file_path: Path, content: str):
        """Parse Terraform state file"""
        try:
            state = json.loads(content)

            # Extract resources from state
            if "resources" in state:
                for resource in state["resources"]:
                    node_id = f"state_{resource['type']}_{resource['name']}"
                    node = TerraformNode(
                        id=node_id,
                        type="state_resource",
                        name=f"{resource['type']}.{resource['name']}",
                        source_file=str(file_path),
                        properties={
                            "resource_type": resource["type"],
                            "mode": resource.get("mode", "managed"),
                            "provider": resource.get("provider", ""),
                            "instances": len(resource.get("instances", [])),
                        },
                        labels=["TerraformState", "Resource"],
                    )
                    self.nodes[node_id] = node

        except json.JSONDecodeError as e:
            print(f"Warning: Invalid JSON in state file {file_path}: {e}")

    def _parse_tfvars_file(self, file_path: Path, content: str):
        """Parse Terraform variables file"""
        try:
            parsed = hcl2.loads(content)

            for key, value in parsed.items():
                node_id = f"tfvar_{key}_{file_path.stem}"
                node = TerraformNode(
                    id=node_id,
                    type="tfvar",
                    name=key,
                    source_file=str(file_path),
                    properties={"value": str(value), "file_type": "tfvars"},
                    labels=["TerraformVariable", "Configuration"],
                )
                self.nodes[node_id] = node

        except Exception as e:
            print(f"Warning: Failed to parse tfvars file {file_path}: {e}")

    def _extract_terraform_elements(self, file_path: Path, parsed: Dict[str, Any]):
        """Extract all Terraform elements from parsed HCL"""

        # Extract variables
        if "variable" in parsed:
            variables = parsed["variable"]
            if isinstance(variables, list):
                for var_block in variables:
                    if isinstance(var_block, dict):
                        for var_name, var_config in var_block.items():
                            self._create_variable_node(file_path, var_name, var_config)
            elif isinstance(variables, dict):
                for var_name, var_config in variables.items():
                    self._create_variable_node(file_path, var_name, var_config)

        # Extract locals
        if "locals" in parsed:
            locals_data = parsed["locals"]
            if isinstance(locals_data, list):
                for locals_block in locals_data:
                    if isinstance(locals_block, dict):
                        for local_name, local_value in locals_block.items():
                            self._create_local_node(file_path, local_name, local_value)
            elif isinstance(locals_data, dict):
                for local_name, local_value in locals_data.items():
                    self._create_local_node(file_path, local_name, local_value)

        # Extract data sources
        if "data" in parsed:
            data_sources = parsed["data"]
            if isinstance(data_sources, list):
                for data_block in data_sources:
                    if isinstance(data_block, dict):
                        for data_type, data_configs in data_block.items():
                            if isinstance(data_configs, dict):
                                for data_name, data_config in data_configs.items():
                                    self._create_data_node(
                                        file_path, data_type, data_name, data_config
                                    )
            elif isinstance(data_sources, dict):
                for data_type, data_configs in data_sources.items():
                    if isinstance(data_configs, dict):
                        for data_name, data_config in data_configs.items():
                            self._create_data_node(
                                file_path, data_type, data_name, data_config
                            )

        # Extract resources
        if "resource" in parsed:
            resources = parsed["resource"]
            if isinstance(resources, list):
                for resource_block in resources:
                    if isinstance(resource_block, dict):
                        for resource_type, resource_configs in resource_block.items():
                            if isinstance(resource_configs, dict):
                                for (
                                    resource_name,
                                    resource_config,
                                ) in resource_configs.items():
                                    self._create_resource_node(
                                        file_path,
                                        resource_type,
                                        resource_name,
                                        resource_config,
                                    )
            elif isinstance(resources, dict):
                for resource_type, resource_configs in resources.items():
                    if isinstance(resource_configs, dict):
                        for resource_name, resource_config in resource_configs.items():
                            self._create_resource_node(
                                file_path, resource_type, resource_name, resource_config
                            )

        # Extract modules
        if "module" in parsed:
            modules = parsed["module"]
            if isinstance(modules, list):
                for module_block in modules:
                    if isinstance(module_block, dict):
                        for module_name, module_config in module_block.items():
                            self._create_module_node(
                                file_path, module_name, module_config
                            )
            elif isinstance(modules, dict):
                for module_name, module_config in modules.items():
                    self._create_module_node(file_path, module_name, module_config)

        # Extract outputs
        if "output" in parsed:
            outputs = parsed["output"]
            if isinstance(outputs, list):
                for output_block in outputs:
                    if isinstance(output_block, dict):
                        for output_name, output_config in output_block.items():
                            self._create_output_node(
                                file_path, output_name, output_config
                            )
            elif isinstance(outputs, dict):
                for output_name, output_config in outputs.items():
                    self._create_output_node(file_path, output_name, output_config)

    def _create_variable_node(self, file_path: Path, name: str, config: Dict[str, Any]):
        """Create a variable node"""
        node_id = f"var_{name}_{file_path.stem}"
        node = TerraformNode(
            id=node_id,
            type="variable",
            name=name,
            source_file=str(file_path),
            properties={
                "description": config.get("description", ""),
                "type": str(config.get("type", "any")),
                "default": str(config.get("default", "")),
                "sensitive": config.get("sensitive", False),
                "validation": bool(config.get("validation")),
            },
            labels=["TerraformVariable", "Input"],
        )
        self.nodes[node_id] = node

    def _create_local_node(self, file_path: Path, name: str, value: Any):
        """Create a local value node"""
        node_id = f"local_{name}_{file_path.stem}"
        node = TerraformNode(
            id=node_id,
            type="local",
            name=name,
            source_file=str(file_path),
            properties={"value": str(value), "computed": True},
            labels=["TerraformLocal", "Computed"],
        )
        self.nodes[node_id] = node

    def _create_data_node(
        self, file_path: Path, data_type: str, name: str, config: Dict[str, Any]
    ):
        """Create a data source node"""
        node_id = f"data_{data_type}_{name}_{file_path.stem}"
        node = TerraformNode(
            id=node_id,
            type="data",
            name=f"{data_type}.{name}",
            source_file=str(file_path),
            properties={
                "data_type": data_type,
                "provider": data_type.split("_")[0] if "_" in data_type else "unknown",
                "config_keys": list(config.keys()) if isinstance(config, dict) else [],
            },
            labels=["TerraformData", "DataSource"],
        )
        self.nodes[node_id] = node

    def _create_resource_node(
        self, file_path: Path, resource_type: str, name: str, config: Dict[str, Any]
    ):
        """Create a resource node"""
        node_id = f"resource_{resource_type}_{name}_{file_path.stem}"

        # Detect if resource uses count or for_each
        has_count = "count" in config
        has_for_each = "for_each" in config

        node = TerraformNode(
            id=node_id,
            type="resource",
            name=f"{resource_type}.{name}",
            source_file=str(file_path),
            properties={
                "resource_type": resource_type,
                "provider": (
                    resource_type.split("_")[0] if "_" in resource_type else "unknown"
                ),
                "has_count": has_count,
                "has_for_each": has_for_each,
                "has_depends_on": "depends_on" in config,
                "config_keys": list(config.keys()) if isinstance(config, dict) else [],
            },
            labels=["TerraformResource", "Infrastructure"],
        )
        self.nodes[node_id] = node

        # Store depends_on relationships for later processing
        if "depends_on" in config:
            depends_on = config["depends_on"]
            if isinstance(depends_on, list):
                for dependency in depends_on:
                    self.variable_references[node_id].add(str(dependency))

    def _create_module_node(self, file_path: Path, name: str, config: Dict[str, Any]):
        """Create a module node"""
        node_id = f"module_{name}_{file_path.stem}"

        source = config.get("source", "")

        node = TerraformNode(
            id=node_id,
            type="module",
            name=name,
            source_file=str(file_path),
            properties={
                "source": source,
                "is_local": source.startswith("./") or source.startswith("../"),
                "is_registry": not source.startswith("./")
                and not source.startswith("../")
                and "/" in source,
                "input_variables": [k for k in config.keys() if k != "source"],
            },
            labels=["TerraformModule", "ModuleCall"],
        )
        self.nodes[node_id] = node

        # Store module call for relationship building
        self.module_calls[node_id] = config

    def _create_output_node(self, file_path: Path, name: str, config: Dict[str, Any]):
        """Create an output node"""
        node_id = f"output_{name}_{file_path.stem}"
        node = TerraformNode(
            id=node_id,
            type="output",
            name=name,
            source_file=str(file_path),
            properties={
                "description": config.get("description", ""),
                "sensitive": config.get("sensitive", False),
                "value": str(config.get("value", "")),
            },
            labels=["TerraformOutput", "Export"],
        )
        self.nodes[node_id] = node

        # Extract references from output value
        value = config.get("value", "")
        if isinstance(value, str):
            refs = self._extract_references(value)
            for ref in refs:
                self.variable_references[node_id].add(ref)

    def _extract_references(self, text: str) -> Set[str]:
        """Extract Terraform references from text (var.x, module.y.z, etc.)"""
        references = set()

        # Pattern to match Terraform references
        patterns = [
            r"var\.([a-zA-Z_][a-zA-Z0-9_]*)",
            r"local\.([a-zA-Z_][a-zA-Z0-9_]*)",
            r"module\.([a-zA-Z_][a-zA-Z0-9_]*)\.([a-zA-Z_][a-zA-Z0-9_]*)",
            r"data\.([a-zA-Z_][a-zA-Z0-9_]*)\.([a-zA-Z_][a-zA-Z0-9_]*)",
            r"([a-zA-Z_][a-zA-Z0-9_]*)\.([a-zA-Z_][a-zA-Z0-9_]*)",  # resource references
        ]

        for pattern in patterns:
            matches = re.findall(pattern, str(text))
            for match in matches:
                if isinstance(match, tuple):
                    if len(match) == 2:
                        references.add(f"{match[0]}.{match[1]}")
                    else:
                        references.add(match[0])
                else:
                    references.add(match)

        return references

    def _build_relationships(self):
        """Build relationships between nodes"""
        print("Building relationships...")

        # Build variable reference relationships
        for from_node_id, references in self.variable_references.items():
            for reference in references:
                to_node_id = self._find_node_by_reference(reference)
                if to_node_id:
                    relationship = TerraformRelationship(
                        from_node=from_node_id,
                        to_node=to_node_id,
                        relationship_type="REFERENCES",
                        properties={"reference": reference},
                    )
                    self.relationships.append(relationship)

        # Build module relationships
        for module_node_id, module_config in self.module_calls.items():
            source = module_config.get("source", "")

            # Find module definition nodes
            if source.startswith("./") or source.startswith("../"):
                # Local module - find nodes in the source directory
                module_path = Path(self.terraform_dir) / source
                for node_id, node in self.nodes.items():
                    if str(module_path) in node.source_file:
                        relationship = TerraformRelationship(
                            from_node=module_node_id,
                            to_node=node_id,
                            relationship_type="CALLS_MODULE",
                            properties={"source": source},
                        )
                        self.relationships.append(relationship)

            # Build input variable relationships
            for var_name, var_value in module_config.items():
                if var_name != "source":
                    # Find references in the variable value
                    refs = self._extract_references(str(var_value))
                    for ref in refs:
                        ref_node_id = self._find_node_by_reference(ref)
                        if ref_node_id:
                            relationship = TerraformRelationship(
                                from_node=module_node_id,
                                to_node=ref_node_id,
                                relationship_type="USES_INPUT",
                                properties={"variable": var_name, "reference": ref},
                            )
                            self.relationships.append(relationship)

        # Build module-to-module dependency relationships
        self._build_module_dependencies()

        # Build provider relationships
        self._build_provider_relationships()

        # Build file relationships
        # self._build_file_relationships()

    def _find_node_by_reference(self, reference: str) -> Optional[str]:
        """Find a node ID by its Terraform reference"""
        # Try different node ID patterns
        patterns = [
            f"var_{reference}",
            f"local_{reference}",
            f"data_{reference.replace('.', '_')}",
            f"resource_{reference.replace('.', '_')}",
            f"module_{reference}",
            f"output_{reference}",
        ]

        for pattern in patterns:
            for node_id in self.nodes.keys():
                if pattern in node_id:
                    return node_id

        return None

    def _build_module_dependencies(self):
        """Build module-to-module dependency relationships"""
        print("Building module-to-module dependencies...")

        # Analyze each module call for dependencies on other modules
        for module_node_id, module_config in self.module_calls.items():
            module_name = module_node_id.split("_")[
                1
            ]  # Extract module name from node_id

            # Check each input parameter for module references
            for var_name, var_value in module_config.items():
                if var_name == "source":
                    continue

                # Extract module references from the variable value
                module_refs = self._extract_module_references(str(var_value))

                for module_ref in module_refs:
                    # Find the target module node
                    target_module_id = self._find_module_node_by_name(module_ref)
                    if target_module_id and target_module_id != module_node_id:
                        # Create module dependency relationship
                        relationship = TerraformRelationship(
                            from_node=module_node_id,
                            to_node=target_module_id,
                            relationship_type="MODULE_DEPENDENCY",
                            properties={
                                "dependency_type": "output_reference",
                                "input_variable": var_name,
                                "module_reference": module_ref,
                                "value": str(var_value),
                            },
                        )
                        self.relationships.append(relationship)
                        print(
                            f"  Found module dependency: {module_name} -> {module_ref} (via {var_name})"
                        )

    def _extract_module_references(self, value: str) -> Set[str]:
        """Extract module references from a value string"""
        module_refs = set()

        # Pattern to match module.name.output_name
        import re

        module_pattern = r"module\.([a-zA-Z_][a-zA-Z0-9_-]*)\."
        matches = re.findall(module_pattern, value)

        for match in matches:
            module_refs.add(match)

        return module_refs

    def _find_module_node_by_name(self, module_name: str) -> Optional[str]:
        """Find a module node by its name"""
        # Look for module nodes with the given name
        for node_id, node in self.nodes.items():
            if node.type == "module" and node.name == module_name:
                return node_id
        return None

    def _build_provider_relationships(self):
        """Build relationships between resources and their providers"""
        providers = defaultdict(list)

        # Group nodes by provider
        for node_id, node in self.nodes.items():
            if node.type in ["resource", "data"]:
                provider = node.properties.get("provider", "unknown")
                providers[provider].append(node_id)

        # Create provider nodes and relationships
        for provider, node_ids in providers.items():
            if provider != "unknown" and len(node_ids) > 1:
                provider_node_id = f"provider_{provider}"
                if provider_node_id not in self.nodes:
                    provider_node = TerraformNode(
                        id=provider_node_id,
                        type="provider",
                        name=provider,
                        source_file="computed",
                        properties={"resource_count": len(node_ids)},
                        labels=["TerraformProvider", "Infrastructure"],
                    )
                    self.nodes[provider_node_id] = provider_node

                # Create relationships
                for node_id in node_ids:
                    relationship = TerraformRelationship(
                        from_node=node_id,
                        to_node=provider_node_id,
                        relationship_type="USES_PROVIDER",
                        properties={"provider": provider},
                    )
                    self.relationships.append(relationship)

    def _build_file_relationships(self):
        """Build relationships between nodes in the same file"""
        files = defaultdict(list)

        # Group nodes by file
        for node_id, node in self.nodes.items():
            files[node.source_file].append(node_id)

        # Create file relationships
        for file_path, node_ids in files.items():
            if len(node_ids) > 1:
                # Create relationships between nodes in the same file
                for i, node_id in enumerate(node_ids):
                    for other_node_id in node_ids[i + 1 :]:
                        relationship = TerraformRelationship(
                            from_node=node_id,
                            to_node=other_node_id,
                            relationship_type="SAME_FILE",
                            properties={"file": file_path},
                        )
                        self.relationships.append(relationship)


class Neo4jCypherGenerator:
    """Generates Neo4j Cypher queries from Terraform graph data with performance optimizations"""

    def __init__(
        self,
        batch_size: int = 100,
        use_transactions: bool = True,
        force_cleanup: bool = False,
        cypher_shell_compatible: bool = False,
    ):
        self.cypher_queries = []
        self.batch_size = batch_size
        self.use_transactions = use_transactions
        self.force_cleanup = force_cleanup
        self.cypher_shell_compatible = cypher_shell_compatible

    def generate_cypher(
        self,
        nodes: Dict[str, TerraformNode],
        relationships: List[TerraformRelationship],
    ) -> List[str]:
        """Generate optimized Cypher queries for nodes and relationships"""
        print("Generating optimized Neo4j Cypher queries...")

        # Clear existing queries
        self.cypher_queries = []

        # Add cleanup queries
        self._add_cleanup_queries()

        # Add constraint queries
        self._add_constraint_queries()

        # Generate optimized node creation queries using UNWIND
        self._generate_optimized_node_queries(nodes)

        # Generate optimized relationship queries using UNWIND
        self._generate_optimized_relationship_queries(relationships)

        # Add index creation queries
        self._add_index_queries()

        return self.cypher_queries

    def _add_cleanup_queries(self):
        """Add queries to clean up existing data"""
        if self.force_cleanup:
            # More aggressive cleanup that handles constraints properly
            self.cypher_queries.extend(
                [
                    "// Force cleanup of existing Terraform data",
                    "// Drop constraints first to avoid conflicts",
                    "DROP CONSTRAINT terraform_node_id IF EXISTS;",
                    "DROP CONSTRAINT terraform_resource_name IF EXISTS;",
                    "DROP CONSTRAINT terraform_module_name IF EXISTS;",
                    "DROP CONSTRAINT terraform_variable_name IF EXISTS;",
                    "",
                    "// Delete all Terraform nodes and relationships",
                    "MATCH (n) WHERE any(label IN labels(n) WHERE label STARTS WITH 'Terraform') DETACH DELETE n;",
                    "",
                    "// Clean up any orphaned relationships",
                    "MATCH ()-[r]->() WHERE type(r) IN ['REFERENCES', 'CALLS_MODULE', 'USES_INPUT', 'USES_PROVIDER', 'SAME_FILE'] DELETE r;",
                    "",
                ]
            )
        else:
            # Standard cleanup - more conservative
            self.cypher_queries.extend(
                [
                    "// Clean up existing Terraform data",
                    "MATCH (n) WHERE any(label IN labels(n) WHERE label STARTS WITH 'Terraform') DETACH DELETE n;",
                    "",
                ]
            )

    def _add_constraint_queries(self):
        """Add constraint creation queries"""
        if self.cypher_shell_compatible:
            # Skip constraints in cypher-shell compatible mode - MERGE handles uniqueness
            self.cypher_queries.extend(
                ["// Skipping constraints in cypher-shell compatible mode", ""]
            )
            return

        if self.force_cleanup:
            # For force cleanup mode, constraints are already dropped, so just create them
            constraints = [
                "CREATE CONSTRAINT terraform_node_id FOR (n:TerraformNode) REQUIRE n.id IS UNIQUE;",
                "CREATE CONSTRAINT terraform_resource_name FOR (n:TerraformResource) REQUIRE n.name IS UNIQUE;",
                "CREATE CONSTRAINT terraform_module_name FOR (n:TerraformModule) REQUIRE n.name IS UNIQUE;",
                "CREATE CONSTRAINT terraform_variable_name FOR (n:TerraformVariable) REQUIRE n.name IS UNIQUE;",
            ]
        else:
            # Use IF NOT EXISTS for standard mode
            constraints = [
                "CREATE CONSTRAINT terraform_node_id IF NOT EXISTS FOR (n:TerraformNode) REQUIRE n.id IS UNIQUE;",
                "CREATE CONSTRAINT terraform_resource_name IF NOT EXISTS FOR (n:TerraformResource) REQUIRE n.name IS UNIQUE;",
                "CREATE CONSTRAINT terraform_module_name IF NOT EXISTS FOR (n:TerraformModule) REQUIRE n.name IS UNIQUE;",
                "CREATE CONSTRAINT terraform_variable_name IF NOT EXISTS FOR (n:TerraformVariable) REQUIRE n.name IS UNIQUE;",
            ]

        self.cypher_queries.extend(["// Create constraints", *constraints, ""])

    def _generate_optimized_node_queries(self, nodes: Dict[str, TerraformNode]):
        """Generate optimized Cypher queries for creating nodes using UNWIND and batching"""
        self.cypher_queries.append("// Create Terraform nodes using optimized batching")

        # Group nodes by their label combination for more efficient creation
        nodes_by_labels = defaultdict(list)
        for node in nodes.values():
            labels_key = ":".join(["TerraformNode"] + sorted(node.labels))
            nodes_by_labels[labels_key].append(node)

        # Process each label group
        for labels, node_list in nodes_by_labels.items():
            self._create_nodes_batch(labels, node_list)

        self.cypher_queries.append("")

    def _create_nodes_batch(self, labels: str, nodes: List[TerraformNode]):
        """Create nodes in batches using UNWIND for better performance"""
        # Process nodes in batches
        for i in range(0, len(nodes), self.batch_size):
            batch = nodes[i : i + self.batch_size]

            if self.use_transactions and not self.cypher_shell_compatible:
                self.cypher_queries.append(":begin")

            # Create the data array for UNWIND
            node_data = []
            for node in batch:
                properties = self._format_properties_for_unwind(node.properties)
                properties.update(
                    {
                        "id": node.id,
                        "type": node.type,
                        "name": node.name,
                        "source_file": node.source_file,
                    }
                )
                node_data.append(properties)

            # Generate UNWIND query
            self.cypher_queries.append(
                f"// Batch {i//self.batch_size + 1} for {labels}"
            )
            self.cypher_queries.append("UNWIND [")

            for j, props in enumerate(node_data):
                props_str = self._dict_to_cypher_map(props)
                comma = "," if j < len(node_data) - 1 else ""
                self.cypher_queries.append(f"  {props_str}{comma}")

            self.cypher_queries.append("] AS nodeData")
            if self.cypher_shell_compatible:
                # Use MERGE for cypher-shell compatibility to avoid constraint violations
                self.cypher_queries.append(f"MERGE (n:{labels} {{id: nodeData.id}})")
                self.cypher_queries.append("SET n = nodeData;")
            else:
                # Use CREATE for Neo4j Browser mode
                self.cypher_queries.append(f"CREATE (n:{labels})")
                self.cypher_queries.append("SET n = nodeData;")

            if self.use_transactions and not self.cypher_shell_compatible:
                self.cypher_queries.append(":commit")

            self.cypher_queries.append("")

    def _generate_optimized_relationship_queries(
        self, relationships: List[TerraformRelationship]
    ):
        """Generate optimized Cypher queries for creating relationships using UNWIND and batching"""
        self.cypher_queries.append("// Create relationships using optimized batching")

        # Group relationships by type for more efficient creation
        relationships_by_type = defaultdict(list)
        for rel in relationships:
            relationships_by_type[rel.relationship_type].append(rel)

        # Process each relationship type
        for rel_type, rel_list in relationships_by_type.items():
            self._create_relationships_batch(rel_type, rel_list)

        self.cypher_queries.append("")

    def _create_relationships_batch(
        self, rel_type: str, relationships: List[TerraformRelationship]
    ):
        """Create relationships in batches using UNWIND for better performance"""
        # Process relationships in batches
        for i in range(0, len(relationships), self.batch_size):
            batch = relationships[i : i + self.batch_size]

            if self.use_transactions and not self.cypher_shell_compatible:
                self.cypher_queries.append(":begin")

            # Create the data array for UNWIND
            rel_data = []
            for rel in batch:
                properties = self._format_properties_for_unwind(rel.properties)
                rel_data.append(
                    {
                        "from_id": rel.from_node,
                        "to_id": rel.to_node,
                        "properties": properties,
                    }
                )

            # Generate UNWIND query
            self.cypher_queries.append(
                f"// Batch {i//self.batch_size + 1} for {rel_type} relationships"
            )
            self.cypher_queries.append("UNWIND [")

            for j, rel_props in enumerate(rel_data):
                rel_str = self._dict_to_cypher_map(rel_props)
                comma = "," if j < len(rel_data) - 1 else ""
                self.cypher_queries.append(f"  {rel_str}{comma}")

            self.cypher_queries.append("] AS relData")
            self.cypher_queries.append(
                "MATCH (from:TerraformNode {id: relData.from_id})"
            )
            self.cypher_queries.append("MATCH (to:TerraformNode {id: relData.to_id})")

            if rel_data and rel_data[0]["properties"]:
                self.cypher_queries.append(f"CREATE (from)-[r:{rel_type}]->(to)")
                self.cypher_queries.append("SET r = relData.properties;")
            else:
                self.cypher_queries.append(f"CREATE (from)-[:{rel_type}]->(to);")

            if self.use_transactions and not self.cypher_shell_compatible:
                self.cypher_queries.append(":commit")

            self.cypher_queries.append("")

    def _add_index_queries(self):
        """Add index creation queries for better performance"""
        indexes = [
            "CREATE INDEX terraform_node_type IF NOT EXISTS FOR (n:TerraformNode) ON (n.type);",
            "CREATE INDEX terraform_node_name IF NOT EXISTS FOR (n:TerraformNode) ON (n.name);",
            "CREATE INDEX terraform_resource_type IF NOT EXISTS FOR (n:TerraformResource) ON (n.resource_type);",
            "CREATE INDEX terraform_module_source IF NOT EXISTS FOR (n:TerraformModule) ON (n.source);",
        ]

        self.cypher_queries.extend(["// Create indexes for performance", *indexes, ""])

    def _format_properties(self, properties: Dict[str, Any]) -> Dict[str, str]:
        """Format properties for Cypher query"""
        formatted = {}

        for key, value in properties.items():
            if isinstance(value, bool):
                formatted[key] = str(value).lower()
            elif isinstance(value, (int, float)):
                formatted[key] = str(value)
            elif isinstance(value, list):
                # Convert list to string representation
                list_items = [f'"{self._escape_string(str(item))}"' for item in value]
                formatted[key] = f"[{', '.join(list_items)}]"
            else:
                formatted[key] = f'"{self._escape_string(str(value))}"'

        return formatted

    def _format_properties_for_unwind(
        self, properties: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Format properties for UNWIND operations (no quotes needed)"""
        formatted = {}

        for key, value in properties.items():
            if isinstance(value, bool):
                formatted[key] = value
            elif isinstance(value, (int, float)):
                formatted[key] = value
            elif isinstance(value, list):
                # Keep as list for UNWIND
                formatted[key] = [str(item) for item in value]
            else:
                formatted[key] = str(value)

        return formatted

    def _dict_to_cypher_map(self, data: Dict[str, Any]) -> str:
        """Convert dictionary to Cypher map format"""
        items = []
        for key, value in data.items():
            if isinstance(value, dict):
                # Nested dictionary
                nested = self._dict_to_cypher_map(value)
                items.append(f"{key}: {nested}")
            elif isinstance(value, list):
                # List of values
                list_items = [f'"{self._escape_string(str(item))}"' for item in value]
                items.append(f'{key}: [{", ".join(list_items)}]')
            elif isinstance(value, bool):
                items.append(f"{key}: {str(value).lower()}")
            elif isinstance(value, (int, float)):
                items.append(f"{key}: {value}")
            else:
                items.append(f'{key}: "{self._escape_string(str(value))}"')

        return "{" + ", ".join(items) + "}"

    def _escape_string(self, text: str) -> str:
        """Escape special characters in strings for Cypher"""
        if not isinstance(text, str):
            text = str(text)

        # Escape quotes and backslashes
        text = text.replace("\\", "\\\\")
        text = text.replace('"', '\\"')
        text = text.replace("\n", "\\n")
        text = text.replace("\r", "\\r")
        text = text.replace("\t", "\\t")

        return text

    def save_to_file(self, output_file: str):
        """Save Cypher queries to file"""
        with open(output_file, "w", encoding="utf-8") as f:
            f.write("\n".join(self.cypher_queries))
        print(f"Cypher queries saved to {output_file}")


def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(
        description="Analyze Terraform infrastructure and generate Neo4j Cypher queries"
    )
    parser.add_argument(
        "--terraform-dir",
        default="terraform",
        help="Directory containing Terraform files (default: terraform)",
    )
    parser.add_argument(
        "--output",
        default="scripts/neo4j/output/terraform_graph.cypher",
        help="Output file for Cypher queries (default: scripts/neo4j/output/terraform_graph.cypher)",
    )
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    parser.add_argument(
        "--batch-size",
        type=int,
        default=100,
        help="Batch size for Cypher query generation (default: 100)",
    )
    parser.add_argument(
        "--no-transactions",
        action="store_true",
        help="Disable transaction wrapping for batch operations",
    )
    parser.add_argument(
        "--force-cleanup",
        action="store_true",
        help="Force cleanup of existing data before import",
    )
    parser.add_argument(
        "--cypher-shell-compatible",
        action="store_true",
        help="Generate cypher-shell compatible output (no browser-specific commands)",
    )

    args = parser.parse_args()

    # Check if terraform directory exists
    if not Path(args.terraform_dir).exists():
        print(f"Error: Terraform directory '{args.terraform_dir}' does not exist")
        sys.exit(1)

    try:
        # Analyze Terraform files
        analyzer = TerraformAnalyzer(args.terraform_dir)
        nodes, relationships = analyzer.analyze()

        print(f"Analysis complete:")
        print(f"  - Found {len(nodes)} nodes")
        print(f"  - Found {len(relationships)} relationships")

        if args.verbose:
            # Print summary by type
            node_types = defaultdict(int)
            for node in nodes.values():
                node_types[node.type] += 1

            print("\nNode types:")
            for node_type, count in sorted(node_types.items()):
                print(f"  - {node_type}: {count}")

            rel_types = defaultdict(int)
            for rel in relationships:
                rel_types[rel.relationship_type] += 1

            print("\nRelationship types:")
            for rel_type, count in sorted(rel_types.items()):
                print(f"  - {rel_type}: {count}")

        # Generate Cypher queries with optimization settings
        generator = Neo4jCypherGenerator(
            batch_size=args.batch_size,
            use_transactions=not args.no_transactions,
            force_cleanup=args.force_cleanup,
            cypher_shell_compatible=args.cypher_shell_compatible,
        )
        cypher_queries = generator.generate_cypher(nodes, relationships)

        # Save to file
        generator.save_to_file(args.output)

        print(f"\nGenerated {len(cypher_queries)} Cypher statements")
        print(f"To import into Neo4j:")
        print(f"  1. Start Neo4j database")
        print(f"  2. Open Neo4j Browser")
        print(f"  3. Copy and paste the contents of '{args.output}'")
        print(f"  4. Execute the queries")
        print(f"\nAlternatively, use cypher-shell:")
        print(f"  cypher-shell -f {args.output}")

    except Exception as e:
        print(f"Error: {e}")
        if args.verbose:
            import traceback

            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
