#!/bin/bash

# Local Development Environment Testing Script
# Usage: ./scripts/test-local-deployment.sh [--verbose] [--quick]

# Source utility functions
source "$(dirname "$0")/utils.sh"

# Parse command line arguments
VERBOSE=false
QUICK_TEST=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --verbose)
            VERBOSE=true
            shift
            ;;
        --quick)
            QUICK_TEST=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [--verbose] [--quick]"
            echo "  --verbose   Show detailed output for each test"
            echo "  --quick     Run only essential tests"
            echo "  --help      Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option $1"
            exit 1
            ;;
    esac
done

print_status "Starting local development environment tests..."

# Test configuration
API_URL="http://localhost:3000"
LOCALSTACK_URL="http://localhost:4566"
POSTGRES_HOST="localhost"
POSTGRES_PORT="5432"

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0
TOTAL_TESTS=0

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    print_test "Running: $test_name"
    
    if [ "$VERBOSE" = true ]; then
        echo "  Command: $test_command"
    fi
    
    if eval "$test_command" >/dev/null 2>&1; then
        if [ -n "$expected_result" ]; then
            # Additional validation if expected result is provided
            if eval "$expected_result" >/dev/null 2>&1; then
                echo -e "  ${GREEN}✓ PASSED${NC}"
                TESTS_PASSED=$((TESTS_PASSED + 1))
            else
                echo -e "  ${RED}✗ FAILED${NC} (validation failed)"
                TESTS_FAILED=$((TESTS_FAILED + 1))
            fi
        else
            echo -e "  ${GREEN}✓ PASSED${NC}"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        fi
    else
        echo -e "  ${RED}✗ FAILED${NC}"
        if [ "$VERBOSE" = true ]; then
            echo "  Error details:"
            eval "$test_command" 2>&1 | sed 's/^/    /'
        fi
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    echo
}

# Test 1: Docker Services Status
run_test "Docker Services Running" \
    "docker-compose ps --services --filter status=running | grep -q backend" \
    ""

# Test 2: PostgreSQL Connectivity
run_test "PostgreSQL Database Connectivity" \
    "docker-compose exec -T postgres pg_isready -U ivent_user -d ivent_dev" \
    ""

# Test 3: LocalStack Health
run_test "LocalStack Service Health" \
    "curl -f -s $LOCALSTACK_URL/_localstack/health" \
    ""

# Test 4: API Health Check
run_test "API Health Check Endpoint" \
    "curl -f -s $API_URL/health" \
    ""

# Test 5: API Health Check Response Content
run_test "API Health Check Response Content" \
    "response=\$(curl -s $API_URL/health); echo \"Response: \$response\"" \
    "[[ \$response == *\"Hello World\"* ]] || [[ \$response == *\"OK\"* ]]"

# Test 6: API Documentation
run_test "API Documentation Endpoint" \
    "curl -f -s $API_URL/api" \
    ""

# Test 7: Swagger UI Content
run_test "Swagger UI Content" \
    "response=\$(curl -s $API_URL/api); echo \"Swagger response received\"" \
    "[[ \$response == *\"swagger\"* ]] || [[ \$response == *\"Swagger\"* ]]"

# Test 8: API JSON Schema
run_test "API JSON Schema" \
    "curl -f -s $API_URL/api-json" \
    ""

# Test 9: S3 Bucket Accessibility (LocalStack)
run_test "LocalStack S3 Bucket Accessibility" \
    "aws --endpoint-url=$LOCALSTACK_URL s3 ls s3://ivent-media-dev" \
    ""

# Test 10: S3 Folder Structure
run_test "S3 Folder Structure Verification" \
    "aws --endpoint-url=$LOCALSTACK_URL s3 ls s3://ivent-media-dev/ | grep -q vibes" \
    ""

if [ "$QUICK_TEST" = false ]; then
    # Test 11: Database Connection via psql
    run_test "Database Connection Test" \
        "docker-compose exec -T postgres psql -U ivent_user -d ivent_dev -c 'SELECT 1;'" \
        ""

    # Test 12: Database Tables Existence
    run_test "Database Tables Verification" \
        "docker-compose exec -T postgres psql -U ivent_user -d ivent_dev -c '\\dt' | grep -q 'public'" \
        ""

    # Test 13: Redis Connectivity
    run_test "Redis Service Connectivity" \
        "docker-compose exec -T redis redis-cli ping" \
        ""

    # Test 14: Response Time Test
    run_test "API Response Time Test (< 2 seconds)" \
        "response_time=\$(curl -o /dev/null -s -w '%{time_total}' $API_URL/health); echo \"Response time: \${response_time}s\"" \
        "[[ \$(echo \"\$response_time < 2\" | bc -l 2>/dev/null || echo 0) -eq 1 ]]"

    # Test 15: CORS Headers Test
    run_test "CORS Headers Test" \
        "curl -H \"Origin: http://localhost:3001\" -H \"Access-Control-Request-Method: GET\" -H \"Access-Control-Request-Headers: X-Requested-With\" -X OPTIONS -s $API_URL/health" \
        ""

    # Test 16: File Upload Test (if endpoint exists)
    run_test "File Upload Endpoint Availability" \
        "curl -f -s -X POST $API_URL/upload -F 'file=@package.json' || true" \
        ""

    # Test 17: Environment Variables Test
    run_test "Environment Variables Configuration" \
        "docker-compose exec -T backend printenv | grep -q POSTGRES_HOST" \
        ""
fi

# Summary
echo "=================================="
echo "LOCAL DEVELOPMENT TEST SUMMARY"
echo "=================================="
echo "Environment: Local Docker Development"
echo "API URL: $API_URL"
echo "LocalStack URL: $LOCALSTACK_URL"
echo "Total Tests: $TOTAL_TESTS"
echo -e "Passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Failed: ${RED}$TESTS_FAILED${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 ALL TESTS PASSED! Local development environment is healthy.${NC}"
    
    # Print useful URLs
    echo -e "\n${BLUE}Development Environment URLs:${NC}"
    echo "• Application: $API_URL"
    echo "• Health Check: $API_URL/health"
    echo "• API Documentation: $API_URL/api"
    echo "• LocalStack Dashboard: $LOCALSTACK_URL"
    echo "• PostgreSQL: $POSTGRES_HOST:$POSTGRES_PORT"
    
    echo -e "\n${BLUE}Quick Commands:${NC}"
    echo "• View logs: docker-compose logs -f backend"
    echo "• Access database: docker-compose exec postgres psql -U ivent_user -d ivent_dev"
    echo "• Test S3: aws --endpoint-url=$LOCALSTACK_URL s3 ls"
    
    exit 0
else
    echo -e "\n${RED}❌ $TESTS_FAILED TESTS FAILED! Please check the local development environment.${NC}"
    
    # Print troubleshooting tips
    echo -e "\n${YELLOW}Troubleshooting Tips:${NC}"
    echo "1. Check service logs: docker-compose logs [service-name]"
    echo "2. Restart services: docker-compose restart"
    echo "3. Check service status: docker-compose ps"
    echo "4. Rebuild images: docker-compose build --no-cache"
    echo "5. Clean restart: ./scripts/setup-local-dev.sh --clean"
    
    exit 1
fi
