#!/bin/bash

# Comprehensive Local Development Environment Setup Script
# Usage: ./scripts/setup-local-dev.sh [--clean] [--no-build] [--tools]

# Source utility functions
source "$(dirname "$0")/utils.sh"

# Parse command line arguments
CLEAN_START=false
NO_BUILD=false
INCLUDE_TOOLS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --clean)
            CLEAN_START=true
            shift
            ;;
        --no-build)
            NO_BUILD=true
            shift
            ;;
        --tools)
            INCLUDE_TOOLS=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [--clean] [--no-build] [--tools]"
            echo "  --clean     Clean start (remove all containers and volumes)"
            echo "  --no-build  Skip building Docker images"
            echo "  --tools     Include development tools (pgAdmin)"
            echo "  --help      Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option $1"
            exit 1
            ;;
    esac
done

print_status "Setting up local development environment for ivent-api..."

# Check prerequisites
print_status "Checking prerequisites..."
command -v docker >/dev/null 2>&1 || { print_error "Docker is required but not installed. Please install Docker first."; exit 1; }
command -v docker-compose >/dev/null 2>&1 || { print_error "Docker Compose is required but not installed. Please install Docker Compose first."; exit 1; }

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi

# Clean start if requested
if [ "$CLEAN_START" = true ]; then
    print_status "Performing clean start - removing all containers and volumes..."
    docker-compose down -v --remove-orphans

    # Clean up any stuck processes and containers
    pkill -f localstack 2>/dev/null || true
    docker container prune -f
    docker volume prune -f
    docker system prune -f

    print_status "Clean start completed."
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    print_status "Creating .env file from .env.local template..."
    cp .env.local .env
    print_warning "Please review and update .env file with your specific configuration."
else
    print_status ".env file already exists."
fi

# Make LocalStack init script executable
chmod +x docker/localstack/init/setup-s3.sh

# Build Docker images if not skipped
if [ "$NO_BUILD" = false ]; then
    print_status "Building Docker images..."
    if [ "$INCLUDE_TOOLS" = true ]; then
        docker-compose --profile tools build
    else
        docker-compose build
    fi
else
    print_status "Skipping Docker image build."
fi

# Start the services
print_status "Starting Docker services..."
if [ "$INCLUDE_TOOLS" = true ]; then
    docker-compose --profile tools up -d
else
    docker-compose up -d postgres localstack redis
fi

# Wait for services to be healthy
print_status "Waiting for services to be ready..."

# Wait for PostgreSQL
print_status "Waiting for PostgreSQL to be ready..."
timeout=60
counter=0
until docker-compose exec -T postgres pg_isready -U ivent_user -d ivent_dev >/dev/null 2>&1; do
    if [ $counter -ge $timeout ]; then
        print_error "PostgreSQL failed to start within $timeout seconds"
        exit 1
    fi
    echo -n "."
    sleep 1
    counter=$((counter + 1))
done
echo ""
print_status "PostgreSQL is ready!"

# Wait for LocalStack with integrated error handling
print_status "Waiting for LocalStack to be ready (this may take up to 3 minutes)..."
timeout=180
counter=0
localstack_retry_count=0
max_retries=2

while [ $counter -lt $timeout ]; do
    if curl -s http://localhost:4566/_localstack/health >/dev/null 2>&1; then
        echo ""
        print_status "LocalStack is ready!"
        break
    fi

    if [ $counter -ge $timeout ]; then
        print_error "LocalStack failed to start within $timeout seconds"
        print_error "Checking LocalStack logs..."
        docker-compose logs --tail=30 localstack

        # Check for common issues and attempt recovery
        if docker-compose logs localstack | grep -q "Device or resource busy\|read-only file system"; then
            if [ $localstack_retry_count -lt $max_retries ]; then
                print_warning "LocalStack filesystem issue detected. Attempting recovery..."
                localstack_retry_count=$((localstack_retry_count + 1))

                # Stop and clean LocalStack
                docker-compose down localstack
                docker container rm -f ivent_localstack 2>/dev/null || true
                docker volume rm ivent_api_localstack_data 2>/dev/null || true
                pkill -f localstack 2>/dev/null || true
                sleep 5

                # Recreate and restart
                docker volume create ivent_api_localstack_data
                docker-compose up -d localstack

                # Reset counter for retry
                counter=0
                print_status "LocalStack restarted. Waiting for it to be ready..."
                continue
            else
                print_error "LocalStack recovery failed after $max_retries attempts."
                print_error "Please try:"
                echo "1. Restart Docker Desktop completely"
                echo "2. Run setup with --clean flag: ./scripts/setup-local-dev.sh --clean"
                echo "3. Check Docker Desktop memory allocation (increase to 4GB+)"
                exit 1
            fi
        else
            print_error "LocalStack startup failed. Check the logs above for details."
            exit 1
        fi
    fi

    if [ $((counter % 15)) -eq 0 ] && [ $counter -gt 0 ]; then
        echo ""
        print_status "Still waiting for LocalStack... ($counter/$timeout seconds)"
    else
        echo -n "."
    fi

    sleep 3
    counter=$((counter + 3))
done

if [ $counter -ge $timeout ]; then
    print_error "LocalStack failed to start within $timeout seconds"
    exit 1
fi

# Setup LocalStack S3 buckets
print_status "Setting up LocalStack S3 buckets..."
./scripts/setup-local-s3.sh

# Run database migrations
print_status "Running database migrations..."
if docker-compose exec -T backend npm run migration:run; then
    print_status "Database migrations completed successfully!"
else
    print_warning "Database migrations failed. This might be normal if no migrations exist yet."
fi

# Start the backend service with integrated error handling
print_status "Starting backend service..."
docker-compose up -d backend

# Wait for backend to be healthy with recovery logic
print_status "Waiting for backend service to be ready..."
timeout=150
counter=0
backend_retry_count=0
max_backend_retries=2

while [ $counter -lt $timeout ]; do
    if curl -s http://localhost:3000/health >/dev/null 2>&1; then
        echo ""
        print_status "Backend service is ready!"
        break
    fi

    if [ $counter -ge $timeout ]; then
        print_error "Backend service failed to start within $timeout seconds"
        print_error "Checking backend logs..."
        docker-compose logs --tail=30 backend

        # Check for common issues and attempt recovery
        if docker-compose logs backend | grep -q "read-only file system\|unable to start container process\|EACCES"; then
            if [ $backend_retry_count -lt $max_backend_retries ]; then
                print_warning "Backend container issue detected. Attempting recovery..."
                backend_retry_count=$((backend_retry_count + 1))

                # Stop and clean backend
                docker-compose down backend
                docker container rm -f ivent_api 2>/dev/null || true
                docker volume rm ivent_api_node_modules 2>/dev/null || true

                # Rebuild and restart
                print_status "Rebuilding backend container..."
                docker-compose build --no-cache backend
                docker-compose up -d backend

                # Reset counter for retry
                counter=0
                print_status "Backend restarted. Waiting for it to be ready..."
                continue
            else
                print_error "Backend recovery failed after $max_backend_retries attempts."
                print_error "Please try:"
                echo "1. Restart Docker Desktop completely"
                echo "2. Check Docker Desktop > Preferences > Resources > File sharing"
                echo "3. Ensure $(pwd) is in shared directories"
                echo "4. Run setup with --clean flag: ./scripts/setup-local-dev.sh --clean"
                exit 1
            fi
        else
            print_error "Backend startup failed. Check the logs above for details."
            exit 1
        fi
    fi

    if [ $((counter % 10)) -eq 0 ] && [ $counter -gt 0 ]; then
        echo ""
        print_status "Still waiting for backend... ($counter/$timeout seconds)"
    else
        echo -n "."
    fi

    sleep 2
    counter=$((counter + 2))
done

if [ $counter -ge $timeout ]; then
    print_error "Backend service failed to start within $timeout seconds"
    exit 1
fi

# Display success message and useful information
echo ""
echo "=================================="
echo "🎉 LOCAL DEVELOPMENT ENVIRONMENT READY!"
echo "=================================="
echo ""
print_status "Services are running and healthy!"
echo ""
echo "📋 Available Services:"
echo "  • API Server:          http://localhost:3000"
echo "  • Health Check:        http://localhost:3000/health"
echo "  • API Documentation:   http://localhost:3000/api"
echo "  • PostgreSQL:          localhost:5432"
echo "  • LocalStack (S3):     http://localhost:4566"
echo "  • Redis:               localhost:6379"

if [ "$INCLUDE_TOOLS" = true ]; then
    echo "  • pgAdmin:             http://localhost:5050"
    echo "    - Email: <EMAIL>"
    echo "    - Password: admin123"
fi

echo ""
echo "🔧 Useful Commands:"
echo "  • View logs:           docker-compose logs -f [service]"
echo "  • Stop services:       docker-compose down"
echo "  • Restart service:     docker-compose restart [service]"
echo "  • Run migrations:      docker-compose exec backend npm run migration:run"
echo "  • Access database:     docker-compose exec postgres psql -U ivent_user -d ivent_dev"
echo "  • Test deployment:     ./scripts/test-local-deployment.sh"
echo ""
echo "📁 Database Connection (for external tools):"
echo "  • Host: localhost"
echo "  • Port: 5432"
echo "  • Database: ivent_dev"
echo "  • Username: ivent_user"
echo "  • Password: dev_password_123"
echo ""
print_status "Happy coding! 🚀"
